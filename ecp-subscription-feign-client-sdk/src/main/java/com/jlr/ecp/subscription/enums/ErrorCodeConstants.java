package com.jlr.ecp.subscription.enums;

import com.jlr.ecp.framework.common.exception.ErrorCode;


/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-002-000-000 段
 */
public final class ErrorCodeConstants {



    // ========== ProductService标准接口-商品配置接口 101001 ==========

    public static final ErrorCode SERIES_CREATE_FAIL = new ErrorCode(105001, "车型年款配置创建失败");
    public static final ErrorCode SERIES_VALUE_EXIST_ERROR = new ErrorCode(105002, "该车型已存在，请重新输入");
    public static final ErrorCode SERIES_UPDATE_FAIL = new ErrorCode(105003, "车型年款配置修改失败");
    public static final ErrorCode SERIES_UPDATE_REVISION_ERROR = new ErrorCode(105004, "车型年款配置已被其他人修改");
    public static final ErrorCode SERIES_DELETE_FAIL = new ErrorCode(105006, "该车型年款配置刪除失败");
    public static final ErrorCode SERIES_MODEL_YEAR_EXISTS_ERROR = new ErrorCode(105007, "车型年款有重复");

    // ========== ProductService标准接口-服务包配置接口 105020 ==========
    public static final ErrorCode PACKAGE_CREATE_FAIL = new ErrorCode(105020, "服务包配置创建失败");
    public static final ErrorCode PACKAGE_CODE_ALREADY_EXIST = new ErrorCode(105021, "该服务包编号已存在，请重新输入");
    public static final ErrorCode PACKAGE_DELETE_FAIL = new ErrorCode(105022, "服务包配置删除失败");
    public static final ErrorCode PACKAGE_NOT_FOUND = new ErrorCode(105023, "服务包配置不存在");

    public static final ErrorCode FILE_SIZE_EXCEED_LIMIT = new ErrorCode(105030, "此文件大小超过2MB");
    public static final ErrorCode PACKAGE_BATCH_UPLOAD_FAIL = new ErrorCode(105031, "批量上传服务包编号失败");
    public static final ErrorCode SERVER_ERROR= new ErrorCode(105032, "无法获取模板文件的URL");
    public static final ErrorCode CLIENT_ID_HEADER_NEEDED= new ErrorCode(105033, "请求头需要传递client-id");
    public static final ErrorCode PACKAGE_CODE_LENGTH_LIMIT = new ErrorCode(105034, "服务包编码不能超过30个字符");

    public static final ErrorCode GET_TOKEN_EXCEPTION = new ErrorCode(106030, "获取token异常");

    // ========== TSDP相关 1001007000 ==========
    public static final ErrorCode TSDP_REQUEST_RATE_TOO_FAST  = new ErrorCode(1001007000, "接口请求过快，请至少1分钟后再试");
    public static final ErrorCode TSDP_REQUEST_FORBIDDEN  = new ErrorCode(1001007001, "接口请求无访问权限，确保请求源地址可信");

    // ========== 联通SIM卡相关 1001008000 ==========
    public static final ErrorCode UNICOM_ICCID_NOT_FOUND  = new ErrorCode(1001008001, "未查询到ICCID配置");

    public static final ErrorCode AMAP_CAR_VIN_NOT_FOUND  = new ErrorCode(1001008002, "暂无未激活数据");

    // ========== PIVI手动更新invoice date 1001009000 ==========
    public static final ErrorCode CAR_VIN_FORMAT_ERROR  = new ErrorCode(1001009001, "VIN需由17位数字及字母组成");

    public static final ErrorCode ECP_CAR_VIN_NOT_FOUND  = new ErrorCode(1001009002, "在ECP中未查到该VIN，无法修改发票日期");

    public static final ErrorCode ECP_ORDER_FOUND  = new ErrorCode(1001009003, "该VIN已被订单关联，无法修改发票日期");

    public static final ErrorCode INVOICE_DATE_FORMAT_ERROR  = new ErrorCode(1001009003, "发票日期格式有误");

    // ========== AMAP手动续费 1001010000 ==========
    public static final ErrorCode AMAP_SINGLE_RENEWAL_REQUEST_EMPTY = new ErrorCode(1001010001, "高德单个续费请求为空");
    public static final ErrorCode AMAP_SINGLE_RENEWAL_CODE_ERROR  = new ErrorCode(1001010002, "高德单个续费年限错误");
    public static final ErrorCode AMAP_SIZE_EXCEED_LIMIT = new ErrorCode(1001010003, "上传文件大小必须小于3MB");
    public static final ErrorCode AMAP_TASK_LINE_UP = new ErrorCode(1001010004, "当前多项任务排队中，请稍后再试");
    public static final ErrorCode AMAP_EXCEL_IS_EMPTY = new ErrorCode(1001010005, "上传文件内容为空");
    public static final ErrorCode AMAP_EXCEL_FORMAT_ERROR = new ErrorCode(1001010006, "上传文件填写格式错误");

    // ========== VIN在线服务初始化 1001020000 ==========
    public static final ErrorCode INITIALIZE_STATUS_SIZE_EXCEED_LIMIT = new ErrorCode(1001020001, "上传文件大小必须小于3MB");
    public static final ErrorCode INITIALIZE_STATUS_FILE_INVALID = new ErrorCode(1001020002, "上传文件必须为Excel");
    public static final ErrorCode INITIALIZE_STATUS_EXCEL_IS_EMPTY = new ErrorCode(1001020003, "上传文件内容为空");
    public static final ErrorCode INITIALIZE_STATUS_EXCEL_UPLOAD_ERROR = new ErrorCode(1001020004, "文件上传失败");
    public static final ErrorCode INITIALIZE_STATUS_EXCEL_FORMAT_ERROR = new ErrorCode(1001020005, "上传文件填写格式错误");

    // ========== APPDUC手动续费 1001011000 ==========
    public static final ErrorCode APPDUC_SINGLE_RENEWAL_REQUEST_EMPTY = new ErrorCode(1001011001, "APPD和联通单个续费时间都为空");

    public static final ErrorCode APPDUC_JLR_ID_EMPTY  = new ErrorCode(1001011002, "JLR Subscription ID不存在");

    public static final ErrorCode APPDUC_ICCID_EMPTY = new ErrorCode(1001011003, "联通的Iccid为空");

    public static final ErrorCode APPDUC_CAR_VIN_EMPTY  = new ErrorCode(1001011004, "未找到该VIN");


    public static final ErrorCode RNR_QUERY_FILE_SIZE_EXCEED_LIMIT = new ErrorCode(1001020010, "上传文件大小必须小于3MB");

    public static final ErrorCode RNR_QUERY_EXCEL_IS_EMPTY = new ErrorCode(1001020011, "上传文件内容为空");
    public static final ErrorCode RNR_QUERY_FILE_INVALID = new ErrorCode(1001020013, "上传文件必须为Excel");
    public static final ErrorCode RNR_QUERY_EXCEL_UPLOAD_ERROR = new ErrorCode(1001020014, "文件上传失败!");
    public static final ErrorCode RNR_QUERY_ICCID_NOT_FOUND = new ErrorCode(1001020015, "未查到VIN对应的ICCID");
    public static final ErrorCode RNR_QUERY_NOT_FOUND_INFO = new ErrorCode(1001020016, "未查到ICCID对应的信息");
    public static final ErrorCode SYNC_CU_ERROR = new ErrorCode(1001020013, "BAU同步联通到期时间异常");

    // ========== BAU 1001030000 ==========

    public static final ErrorCode BAU_TRANSFER_S3_CHECK_FLAG_FAIL = new ErrorCode(1001030000, "S3已经存在flag文件无需上传文件");
    public static final ErrorCode BAU_TRANSFER_OSS_CHECK_FLAG_FAIL = new ErrorCode(1001030001, "OSS上没有flag文件");
    public static final ErrorCode BAU_TRANSFER_OSS_FILE_NOT_FOUND = new ErrorCode(1001030001, "未找到OSS上的CSV文件");

    // ========== ICCID portal端修改 1001040000==========
    //ICCID格式错误
    public static final ErrorCode ICCID_LENGTH_ERROR = new ErrorCode(1001039999, "ICCID需由20位数字组成");
    public static final ErrorCode ICCID_FORMAT_ERROR = new ErrorCode(1001040000, "ICCID格式错误");
    //t_pivi_package表中用户输入的vin未找到
    public static final ErrorCode VIN_NOT_FOUND = new ErrorCode(1001040001, "在ECP数据库中未找到该VIN");
    //上传文件必须在500行以内
    public static final ErrorCode UPLOAD_FILE_LIMIT = new ErrorCode(1001040002, "上传文件必须在500行以内");

    // ========== REMOTE手动续费 1001050000 ==========
    public static final ErrorCode REMOTE_SINGLE_RENEWAL_REQUEST_EMPTY = new ErrorCode(1001050000, "远程车控单个续费请求为空");
    public static final ErrorCode REMOTE_SINGLE_RENEWAL_EXPIRE_DATE_NULL = new ErrorCode(1001050001, "远程车控单个续费时间为空");
    public static final ErrorCode REMOTE_RENEWAL_VIN_NOT_FOUND = new ErrorCode(1001050002, "未查询到该用户对应信息。可引导用户登录小程序后再操作续费");
    public static final ErrorCode REMOTE_RENEWAL_VIN_NOT_PIVI = new ErrorCode(1001050003, "该VIN为非PIVI车机，无需续费");
    public static final ErrorCode REMOTE_RENEWAL_SERVICE_DATA_EMPTY = new ErrorCode(1001050004, "该VIN无remote服务相关数据，无需续费");
    public static final ErrorCode REMOTE_SINGLE_RENEWAL_EXPIRE_DATE_INVALID = new ErrorCode(1001050005, "远程车控单个续费时间非法");


    //==========车型展示名称配置
    public static final ErrorCode SERIES_MAPPING_UPDATE_FAIL = new ErrorCode(1001060001, "车型展示名称配置修改失败");
    public static final ErrorCode SERIES_MAPPING_DELETE_FAIL = new ErrorCode(1001060002, "车型展示名称配置删除失败");
    public static final ErrorCode SERIES_REVERSION_ERROR = new ErrorCode(1001060003, "车型展示名称已被其他人修改");


    //===========代客下单
    public static final ErrorCode ICR_NOT_FOUND = new ErrorCode(1001070001, "未查询到该VIN的信息，请核对VIN或使用ICR账号下单");
    public static final ErrorCode VIN_BIND_ICR_ERROR = new ErrorCode(1001070002, "VIN和ICR的绑定关系或已发生变更，请用户用新ICR登录小程序以更新绑定关系");

    public static final ErrorCode INVOICE_DATE_IS_NULL = new ErrorCode(1001070003, "该车辆无发票日期");
    public static final ErrorCode SERVICE_IS_ACTIVE = new ErrorCode(1001070004, "请勿重复下单");
    public static final ErrorCode VEHICLE_NOT_EXIST = new ErrorCode(1001070005, "VIN对应信息不存在");
    public static final ErrorCode VEHICLE_NOT_PIVI = new ErrorCode(1001070006, "该车辆不是PIVI车机");
    public static final ErrorCode INIT_DATA_ERROR = new ErrorCode(1001070007, "未获取到VIN对应的服务信息");
    public static final ErrorCode INVOICE_EXPIRE_DATE_ERROR = new ErrorCode(1001070008, "车辆服务已超过invoice date+15年");
    public static final ErrorCode AMAP_EXPIRE_DATE_ERROR = new ErrorCode(1001070009, "AMAP到期日小于PIVI在线服务包的到期日");
    public static final ErrorCode RNR_CHECK_ERROR = new ErrorCode(1001070010, "用户未完成实名制认证");
    public static final ErrorCode ICR_FOUND_ERROR = new ErrorCode(1001070011, "该账号未绑车，请稍后再试");
    public static final ErrorCode CALL_DP_ERROR = new ErrorCode(1001070012, "调用DP服务异常");
    public static final ErrorCode GUEST_ORDER_ERROR = new ErrorCode(1001070013, "代客下单查询异常");
    public static final ErrorCode VIN_ICR_NOT_NULL = new ErrorCode(1001070014, "VIN和ICR账号不能同时为空");
    public static final ErrorCode CALL_DP_EMPTY = new ErrorCode(1001070015, "DP返回数据为空");
    public static final ErrorCode ICR_FOUND_EMPTY = new ErrorCode(1001070016, "未查询到该ICR账号相关信息，请核对ICR账号或使用VIN下单");

    //===========手动续费并发控制
    public static final ErrorCode CALL_ORDER_SERVICE_ERROR = new ErrorCode(1001080001, "调用订单服务异常");
    public static final ErrorCode ORDER_IN_TRANSIT_ERROR = new ErrorCode(1001080002, "已有在途订单，请稍后再试");
    public static final ErrorCode GET_LOCK_ERROR = new ErrorCode(1001080003, "未获取到锁，无法调用续费服务");
    public static final ErrorCode CALL_AMAP_RENEWAL_ERROR = new ErrorCode(1001080004, "调用高德续费服务异常");
    public static final ErrorCode CALL_APPD_RENEWAL_ERROR = new ErrorCode(1001080005, "调用AppD续费服务异常");
    public static final ErrorCode CALL_UNICOM_RENEWAL_ERROR = new ErrorCode(1001080006, "调用Unicom续费服务异常");
    public static final ErrorCode CALL_REMOTE_RENEWAL_ERROR = new ErrorCode(1001080007, "调用Remote续费服务异常");

    public static final ErrorCode VIN_INITIALIZED_SUCCESS = new ErrorCode(1001080008, "该VIN已初始化过，无需再次补录");
    public static final ErrorCode VIN_INITIALIZED_PENDING = new ErrorCode(1001080009, "该VIN已在在线服务初始化队列中，无需再次补录");

    public static final ErrorCode VIN_MANUAL_PENDING = new ErrorCode(1001080010, "该VIN已在手动补录队列，无需再次补录");

    //===========到期日查询深度搜索
    public static final ErrorCode GET_EXPIRE_DATE_ERROR = new ErrorCode(1001090001, "暂时无法获取数据，请稍后再试");
    public static final ErrorCode VIN_NOT_FOUND_ERROR = new ErrorCode(1001090002, "未查询到VIN对应的InControl远程车控信息，请检查VIN是否输入正确");
}
