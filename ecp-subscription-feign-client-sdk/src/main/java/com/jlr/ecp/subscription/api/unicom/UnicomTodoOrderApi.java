package com.jlr.ecp.subscription.api.unicom;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.api.bau.dto.CheckedVinResp;
import com.jlr.ecp.subscription.api.subscripiton.dto.FulfilmentPIVICompensationDTO;
import com.jlr.ecp.subscription.api.unicom.dto.GenerateReportRequestV2;
import com.jlr.ecp.subscription.api.unicom.dto.MonthlyRnrBatchQueryDTO;
import com.jlr.ecp.subscription.api.unicom.dto.UnicomBatchQueryPageDTO;
import com.jlr.ecp.subscription.api.unicom.vo.InitResponse;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomRnrBatchQueryListVO;
import com.jlr.ecp.subscription.api.unicom.vo.UnicomTodoOrderVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 履约订单")
public interface UnicomTodoOrderApi {
    String PREFIX = ApiConstants.PREFIX+"/unicom/todo/order";


    @GetMapping(PREFIX+"/findUnicomTodoOrder")
    @Operation(summary = "查询联通需要去关闭的订单")
    @PermitAll
    CommonResult<List<UnicomTodoOrderVO>>  findUnicomTodoOrder(@RequestParam("total") Integer total);


    @PostMapping(PREFIX+"/executeTodoOrder")
    @Operation(summary = "执行待发送订单")
    @PermitAll
    CommonResult<Integer>  executeTodoOrder(@RequestBody UnicomTodoOrderVO unicomTodoOrderVO);

    @GetMapping(PREFIX+"/rnrBatchQueryParseJob")
    @Operation(summary = "解析ICCID查询文件JOB执行")
    CommonResult<Integer> rnrBatchQueryParseJob(@RequestParam("batchNo") Long batchNo) throws Exception;

    @GetMapping(PREFIX+"/selectQueryRecordsByBatchNo")
    @Operation(summary = "根据bactNo查询记录")
    CommonResult<List<Long>> selectQueryRecordsByBatchNo(@RequestParam("batchNo") Long batchNo) ;



    @PostMapping(PREFIX+"/rnrBatchQueryExecutor")
    @Operation(summary = "执行查询ICCID批量查询")
    CommonResult<Integer> rnrBatchQueryExecutor(@RequestBody List<Long> idList);


    @PostMapping(PREFIX+"/rnrQueryPageList")
    @Operation(summary = "联通ICCID批量查询结果分页查询")
    CommonResult<PageResult<UnicomRnrBatchQueryListVO>> getBatchQueryPage(@RequestBody UnicomBatchQueryPageDTO pageDTO);



    @GetMapping(PREFIX+"/selectQueryVinRecordsByJobId")
    @Operation(summary = "根据JobID查询VIN月到期日记录")
    CommonResult<List<Long>> selectQueryVinRecordsByJobId(@RequestParam("jobId") Long jobId) ;



    @PostMapping(PREFIX+"/monthlyRnrBatchQueryExecutor")
    @Operation(summary = "月到期VIN执行查询ICCID批量实名查询")
    CommonResult<Integer> monthlyRnrBatchQueryExecutor(@RequestBody MonthlyRnrBatchQueryDTO monthlyRnrBatchQueryDTO);


    @PostMapping(PREFIX + "/initializeRecords")
    @Operation(summary = "初始化VIN月到期记录（含主表和明细表）")
    CommonResult<InitResponse> initializeRecords(@RequestBody GenerateReportRequestV2 request);

    @GetMapping(PREFIX + "/getInitializedCount")
    @Operation(summary = "根据JobID获取已初始化的明细记录数")
    CommonResult<Integer> getInitializedCount(@RequestParam("jobId") Long jobId);


}
