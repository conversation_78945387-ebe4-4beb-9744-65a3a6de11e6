package com.jlr.ecp.subscription.excel.service.amap.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.web.web.core.util.WebFrameworkUtils;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.AmaPActivateRecord;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.AmaPActivateRecordMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fufil.*;
import com.jlr.ecp.subscription.excel.listener.amap.AmaPManualActivationExcelListener;
import com.jlr.ecp.subscription.excel.pojo.amap.AmaPManualActivationExcel;
import com.jlr.ecp.subscription.excel.pojo.amap.CarVinDownload;
import com.jlr.ecp.subscription.excel.pojo.amap.ManualActivationResultExcel;
import com.jlr.ecp.subscription.excel.service.amap.AmaPManualActivationService;
import com.jlr.ecp.subscription.excel.utils.AmaPExcelUtil;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualActivationImportVO;
import com.jlr.ecp.subscription.excel.vo.amap.AmaPManualUploadResultVO;
import com.jlr.ecp.subscription.file.service.FileService;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.util.FileCheckUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
@Slf4j
public class AmaPManualActivationServiceImpl implements AmaPManualActivationService {

    @Resource
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Resource
    private FileService fileService;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper subscriptionServiceLogMapper;

    @Resource
    private AmaPManualActivationServiceImpl amaPManualActivationServiceImpl;

    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;

    @Resource
    private AmaPActivateRecordMapper amaPActivateRecordMapper;

    private static final String FILE_CODE = "AWS_S3_FILE";

    private static final String RENEW_STATUS = "续费成功";

    private static final String ACTIVATION_STATUS = "激活成功";

    private static final String EXCEL_FORMATTER = ".xlsx";


    /**
     * 下载AMAP没有激活车辆VIN码。
     *
     * @return CommonResult<String> 返回一个包含文件下载路径的结果对象，如果下载为空，则返回相应的提示信息。
     */
    @Override
    public CommonResult<String> downloadCarVin() {
        List<String> carVinExcelFilePaths = new ArrayList<>();
        try {
            List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList = getManualActivationFulfilmentDO();
            carVinExcelFilePaths = getExcelFilePath(vcsOrderFufilmentDOList);
            log.info("下载AMAP没有激活车辆VIN码, vcsOrderFufilmentDOList:{}, carVinExcelFilePaths:{}",
                    vcsOrderFufilmentDOList, carVinExcelFilePaths);
            String uploadS3Path = carVinDownloadS3Path(carVinExcelFilePaths);
            if (StringUtils.isBlank(uploadS3Path)) {
                return CommonResult.error(ErrorCodeConstants.AMAP_CAR_VIN_NOT_FOUND);
            }
            return CommonResult.success(uploadS3Path);
        } catch (Exception e) {
            log.error("下载AMAP没有激活车辆VIN码信息异常:", e);
        } finally {
            try {
                for (String filePath : carVinExcelFilePaths) {
                    if (StringUtils.isNotBlank(filePath)) {
                        Files.delete(Paths.get(filePath));
                    }
                }
            } catch (Exception e) {
                log.error("下载AMAP没有激活车辆VIN码, 删除本地临时文件异常:", e);
            }
        }
        return null;
    }

    /**
     * 根据车辆VIN Excel文件路径列表，确定S3存储路径。
     *
     * @param carVinExcelFilePaths 车辆VIN Excel文件路径的列表。
     * @return 返回相应的S3存储路径，如果列表为空，则返回空字符串。
     */
    private String carVinDownloadS3Path(List<String> carVinExcelFilePaths) {
        if (CollUtil.isEmpty(carVinExcelFilePaths)) {
            return "";
        }
        if (carVinExcelFilePaths.size() == 1) {
            return getUploadExcelCarVinPath(carVinExcelFilePaths.get(0));
        }
        return getUploadZipCarVinPath(carVinExcelFilePaths);
    }

    /**
     * 根据车辆VIN码Excel文件路径获取上传到S3的文件路径。
     * 该方法生成一个唯一的文件名，将给定的Excel文件上传到S3，并返回上传文件的路径。
     *
     * @param carVinExcelFilePath 车辆VIN码Excel文件的本地路径。
     * @return 上传到S3后的文件路径。
     */
    private String getUploadExcelCarVinPath(String carVinExcelFilePath) {
        try {
            File excelFile = new File(carVinExcelFilePath);
            //excel上传s3
            String uploadS3FileName = "ECP Export_Inactive AMAP VIN List-" + System.currentTimeMillis()  + EXCEL_FORMATTER;
            return fileService.createFile(null, "AmaPCarVinDownLoadExcel" +  File.separator + uploadS3FileName,
                    FileUtil.readBytes(excelFile), FILE_CODE);
        } catch (Throwable e) {
            log.error("根据车辆VIN码Excel文件路径获取上传到S3的文件路径,异常:", e);
        }
        return "";
    }

    /**
     * 根据车辆VIN信息Excel文件路径列表，生成并返回ZIP文件的S3存储路径。
     * 该方法首先将多个Excel文件压缩成一个ZIP文件，然后上传到S3存储桶中。
     *
     * @param carVinExcelFilePaths 车辆VIN信息Excel文件路径列表。
     * @return 上传到S3后的ZIP文件路径。
     */
    private String getUploadZipCarVinPath(List<String> carVinExcelFilePaths) {
        try {
            File zipFile = File.createTempFile("AmaPCarVinDownloadZip", ".zip");
            try (FileOutputStream fos = new FileOutputStream(zipFile)) {
                zipFiles(carVinExcelFilePaths, fos);
                // zip上传s3
                String uploadS3FileName = "ECP Export_Inactive AMAP VIN List-" + System.currentTimeMillis() + ".zip";
                return fileService.createFile(null, "AmaPCarVinDownLoadZip" + File.separator + uploadS3FileName,
                        FileUtil.readBytes(zipFile), FILE_CODE);
            }
        } catch (Exception e) {
            log.error("根据车辆VIN信息生成并返回ZIP文件的S3存储路径,异常:", e);
        }
        return "";
    }

    /**
     * 将多个文件压缩到一个ZIP文件中。
     *
     * @param fileNames 文件名列表，这些文件将被压缩。
     * @param fos 输出流，用于写入压缩后的ZIP文件。
     */
    private void zipFiles(List<String> fileNames, FileOutputStream fos) {
        try (ZipOutputStream zos = new ZipOutputStream(fos)) {
            for (String fileName : fileNames) {
                Path path = Paths.get(fileName);
                ZipEntry zipEntry = new ZipEntry(path.getFileName().toString());
                zos.putNextEntry(zipEntry);
                Files.copy(path, zos);
                zos.closeEntry();
            }
        } catch (IOException e) {
            log.error("将多个文件压缩到一个ZIP文件中异常:", e);
        }
    }


    /**
     * 处理AMAP手动激活Excel文件上传。
     *
     * @param file 上传的Excel文件，包含AMAP手动激活的信息。
     * @return CommonResult<String> 返回一个处理结果对象
     */
    @Override
    public CommonResult<AmaPManualUploadResultVO> uploadAmaPActivationExcel(MultipartFile file) {
        AmaPManualActivationExcelListener manualActivationExcelListener = new AmaPManualActivationExcelListener();
        String uploadManualExcelPath = null;
        String uploadResultExcelPath = null;
        List<ManualActivationResultExcel> resultExcels = new ArrayList<>();
        try {
            EasyExcel.read(file.getInputStream(), AmaPManualActivationExcel.class, manualActivationExcelListener)
                    .sheet().doRead();
            List<AmaPManualActivationExcel> manualActivationExcels = manualActivationExcelListener.getAllDataList();
            List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList = getManualActivationFulfilmentDO();
            Map<String, Integer> neeedActivationcarVinMap = needActivationCarVinMap(vcsOrderFufilmentDOList);
            Map<String, List<VcsOrderFufilmentDO>> fufilmentDOMap = getFulfilmentMap(vcsOrderFufilmentDOList);
            Map<String, Integer> uploadActivationExcelMap = getUploadExcelCarVinCountMap(manualActivationExcels);
            log.info("处理AMAP手动激活Excel文件上传, manualActivationExcels:{}, vcsOrderFufilmentDOList:{}, neeedActivationcarVinMap:{}," +
                            " fufilmentDOMap:{}, uploadActivationExcelMap:{}", manualActivationExcels, vcsOrderFufilmentDOList,
                    neeedActivationcarVinMap, fufilmentDOMap, uploadActivationExcelMap);
            // 处理上传的Excel文件，激活成功的carVin更新对应的状态
            resultExcels = amaPManualActivationServiceImpl.processManualActivationExcel(manualActivationExcels,
                    uploadActivationExcelMap, neeedActivationcarVinMap, fufilmentDOMap);
            //只对成功的carVin发送激活通知
            vcsOrderFufilmentDOService.executeOrderStatusSync(getSuccessActivationFulfilmentDOList(fufilmentDOMap, resultExcels));
            // 将手动激活结果的Excel文件上传到S3的FILE存储桶。
            uploadManualExcelPath = uploadManualExcelToS3File(manualActivationExcels);
            uploadResultExcelPath = uploadResultExcelToS3File(resultExcels);
            // 记录文件上传
            addAmaPUploadManualExcelLog(file.getOriginalFilename(), uploadManualExcelPath, uploadResultExcelPath);
        } catch (Exception e) {
            log.error("处理AMAP手动激活Excel文件上传异常:", e);
        }
        return CommonResult.success(buildAmaPManualUploadResultVO(uploadManualExcelPath,
                uploadResultExcelPath,
                file.getOriginalFilename(),
                getResultCountByStatus(resultExcels, true),
                getResultCountByStatus(resultExcels, false)
        ));
    }

    /**
     * 获取成功激活的订单履行信息列表。
     *
     * @param map 映射表，键为车辆识别码，值为订单履行信息列表，用于快速查找特定车辆的订单履行信息。
     * @param resultExcels 手动激活结果的Excel列表，包含每个车辆的激活结果和车辆识别码。
     * @return 成功激活的订单履行信息列表，仅包含满足条件的订单履行信息。
     */
    private List<VcsOrderFufilmentDO> getSuccessActivationFulfilmentDOList(Map<String, List<VcsOrderFufilmentDO>> map,
                                                                           List<ManualActivationResultExcel> resultExcels) {
        log.info("获取成功激活的订单履行信息列表, fulfilmentDOMap:{}, resultExcels:{}", map, resultExcels);
        List<VcsOrderFufilmentDO> resp = new ArrayList<>();
        Set<String> carVinSet = new HashSet<>();
        for (ManualActivationResultExcel resultExcel : resultExcels) {
            if (!EcpExcelActivationEnum.ECP_SUCCESS.getDesc().equals(resultExcel.getEcpActivationResult())) {
                continue;
            }
            if (Objects.isNull(map.get(resultExcel.getCarVin()))) {
                continue;
            }
            if (carVinSet.contains(resultExcel.getCarVin())) {
                continue;
            }
            resp.addAll(map.get(resultExcel.getCarVin()));
            carVinSet.add(resultExcel.getCarVin());
        }
        return resp;
    }

    /**
     * 根据车辆vin号将订单履行信息分组。
     *
     * @param vcsOrderFufilmentDOList 订单履行信息列表。
     * @return 以车辆vin号为键，对应的所有订单履行信息列表为值的映射。
     */
    private Map<String, List<VcsOrderFufilmentDO>> getFulfilmentMap(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList) {
        Map<String, List<VcsOrderFufilmentDO>> fulfilmentMap = new HashMap<>();
        for (VcsOrderFufilmentDO vcsOrderFufilmentDO : vcsOrderFufilmentDOList) {
            List<VcsOrderFufilmentDO> fulfilmentList = fulfilmentMap.getOrDefault(vcsOrderFufilmentDO.getCarVin(),
                    new ArrayList<>());
            fulfilmentList.add(vcsOrderFufilmentDO);
            fulfilmentMap.put(vcsOrderFufilmentDO.getCarVin(), fulfilmentList);
        }
        return fulfilmentMap;
    }

    /**
     * 根据指定的状态，统计手动激活结果列表中满足条件的结果数量。
     * true 统计成功的数量    false统计失败的数量
     *
     * @param resultExcels 手动激活结果的Excel列表
     * @param status 指定的激活状态，true表示统计ACTIVATION_STATUS，false表示统计非ACTIVATION_STATUS
     * @return 满足条件的结果数量
     */
    private int getResultCountByStatus(List<ManualActivationResultExcel> resultExcels, boolean status) {
        int count = 0;
        for (ManualActivationResultExcel resultExcel : resultExcels) {
            if (status) {
                if (ACTIVATION_STATUS.equals(resultExcel.getEcpActivationResult())) {
                    count++;
                }
            } else {
                if (!ACTIVATION_STATUS.equals(resultExcel.getEcpActivationResult())) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 构建AmaPManualUploadResultVO对象，用于封装手动上传文件的结果信息。
     *
     * @param sourceUploadS3File 源文件上传到S3的路径。
     * @param resultUploadS3File 结果文件上传到S3的路径。
     * @param sourceUploadFileName 源文件的原始名称。
     * @param successCount 成功处理的文件数量。
     * @param failedCount 处理失败的文件数量。
     * @return 返回构建好的AmaPManualUploadResultVO对象。
     */
    private AmaPManualUploadResultVO buildAmaPManualUploadResultVO(String sourceUploadS3File,
                                                                   String resultUploadS3File,
                                                                   String sourceUploadFileName,
                                                                   int successCount,
                                                                   int failedCount) {
        return AmaPManualUploadResultVO.builder()
                .sourceUploadS3File(sourceUploadS3File)
                .resultUploadS3File(resultUploadS3File)
                .sourceUploadFileName(sourceUploadFileName)
                .checkResult(getCheckResult(successCount, failedCount))
                .statusDesc("校验完成")
                .successCount(successCount)
                .failedCount(failedCount)
                .build();
    }

    /**
     * 根据成功和失败的数量判断操作的结果。
     *
     * @param successCount 成功的数量。
     * @param failedCount 失败的数量。
     * @return 操作的结果描述。
     */
    private Integer getCheckResult(int successCount, int failedCount) {
        if (successCount == 0 && failedCount == 0) {
            return AmaPExcelCheckResultEnum.PASS.getStatus();
        }
        if (successCount > 0 && failedCount == 0) {
            return AmaPExcelCheckResultEnum.PASS.getStatus();
        }
        if (failedCount > 0 && successCount == 0) {
            return AmaPExcelCheckResultEnum.FAILED.getStatus();
        }
        if (successCount > 0 && failedCount > 0) {
            return AmaPExcelCheckResultEnum.PART_PASS.getStatus();
        }
        return AmaPExcelCheckResultEnum.FAILED.getStatus();
    }

    /**
     * 获取手动激活记录的导入视图对象列表。
     *
     * @return List<AmaPManualActivationImportVO> - 手动激活记录的导入视图对象列表。
     */
    @Override
    public List<AmaPManualActivationImportVO> getAmaPManualActivationImportVO() {
        String userName = WebFrameworkUtils.getLoginUserName();
        if (StringUtils.isBlank(userName)) {
            log.error("获取手动激活记录的导入视图对象列表失败，用户名为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<AmaPActivateRecord> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(AmaPActivateRecord::getIsDeleted, false);
        List<AmaPActivateRecord> amaPActivateRecords = amaPActivateRecordMapper.selectList(queryWrapper);
        return buildAmaPManualActivationImportVO(amaPActivateRecords);
    }

    /**
     * 根据激活记录构建手动激活导入VO列表。
     *
     * @param records 激活记录列表，包含需要手动激活的设备信息。
     * @return 返回一个手动激活导入VO列表，每个VO对应一个激活记录，包含设备的相关信息。
     */
    private List<AmaPManualActivationImportVO> buildAmaPManualActivationImportVO(List<AmaPActivateRecord> records) {
        List<AmaPManualActivationImportVO> activationImportVOS = new ArrayList<>();
        for (AmaPActivateRecord record : records) {
            activationImportVOS.add(buildAmaPManualActivationImportVO(record));
        }
        return activationImportVOS;
    }

    /**
     * 根据激活记录构建AmaPManualActivationImportVO对象。
     *
     * @param amaPActivateRecord 激活记录对象，包含上传文件等相关信息。
     * @return 构建后的AmaPManualActivationImportVO对象，如果输入为null，则返回一个空对象。
     */
    private AmaPManualActivationImportVO buildAmaPManualActivationImportVO(AmaPActivateRecord amaPActivateRecord) {
        if (Objects.isNull(amaPActivateRecord)) {
            return AmaPManualActivationImportVO.builder().build();
        }
        return AmaPManualActivationImportVO.builder()
                .uploadFileName(amaPActivateRecord.getUploadFileName())
                .uploadS3File(amaPActivateRecord.getUploadS3File())
                .resultS3File(amaPActivateRecord.getResultS3File())
                .uploadUserName(amaPActivateRecord.getUploadUserName())
                .uploadTime(TimeFormatUtil.timeToStringByFormat(amaPActivateRecord.getUploadTime(),
                        TimeFormatUtil.formatter_1))
                .build();
    }

    /**
     * 添加手动上传Excel的日志记录。
     *
     * @param uploadName 上传的文件名。
     * @param uploadManualExcelPath 上传的Excel文件在S3存储桶中的路径。
     * @param resultExcelPath 处理结果Excel文件在S3存储桶中的路径。
     */
    private void addAmaPUploadManualExcelLog(String uploadName, String uploadManualExcelPath, String resultExcelPath) {
        String uploadUserName = StringUtils.isNotBlank(WebFrameworkUtils.getLoginUserName()) ?
                WebFrameworkUtils.getLoginUserName() : "admin";
        AmaPActivateRecord amaPActivateRecord = AmaPActivateRecord.builder()
                .uploadFileName(uploadName)
                .uploadS3File(uploadManualExcelPath)
                .resultS3File(resultExcelPath)
                .uploadUserName(uploadUserName)
                .uploadTime(LocalDateTime.now())
                .build();
        amaPActivateRecordMapper.insert(amaPActivateRecord);
    }


    /**
     * 将手动激活的Excel原始文件上传到S3的FILE存储桶。
     *
     * @param activationExcels 手动激活的Excel原始Excel列表。
     * @return 返回上传文件的路径。
     */
    private String uploadManualExcelToS3File(List<AmaPManualActivationExcel> activationExcels) {
        String uploadFilePath = "";
        File uploadManualActivationExcel = null;
        try {
            uploadManualActivationExcel = AmaPExcelUtil.writeManualActivationExcelByGroup(activationExcels);
            uploadFilePath = fileService.createFile(null, "AmaPManualExcel" + File.separator
                    + System.currentTimeMillis() + EXCEL_FORMATTER, FileUtil.readBytes(uploadManualActivationExcel) , FILE_CODE);
            log.info("手动上传激活的Excel的原始文件, 本地文件原始文件路径:{}", uploadManualActivationExcel.getPath());
        } catch (Throwable e) {
            log.error("将手动激活的Excel原始文件上传到S3的FILE存储桶,异常:", e);
        } finally {
            if (Objects.nonNull(uploadManualActivationExcel)) {
                FileUtil.del(uploadManualActivationExcel);
                log.info("手动激活的Excel原始文件上传到S3的FILE存储桶, 成功删除本地临时文件");
            }
        }
        return uploadFilePath;
    }

    /**
     * 将手动激活结果的Excel文件上传到S3的FILE存储桶。
     *
     * @param resultExcels 包含手动激活结果的Excel列表。
     * @return 返回上传文件的路径。
     */
    private String uploadResultExcelToS3File(List<ManualActivationResultExcel> resultExcels) {
        String uploadFilePath = "";
        File uploadManualActivationExcel = null;
        try {
            uploadManualActivationExcel = AmaPExcelUtil.writeResultManualActivationByGroup(resultExcels);
            uploadFilePath = fileService.createFile(null, "ActivationResultExcel" + File.separator
                    + uploadManualActivationExcel.getName(), FileUtil.readBytes(uploadManualActivationExcel) , FILE_CODE);
            log.info("手动激活结果上传到S3的FILE存储桶, 出来结果的文件路径:{}", uploadManualActivationExcel.getPath());
        } catch (Throwable e) {
            log.error("将手动激活结果的Excel文件上传到S3的FILE存储桶,异常:", e);
        } finally {
            if (Objects.nonNull(uploadManualActivationExcel)) {
                FileUtil.del(uploadManualActivationExcel);
                log.info("手动激活结果的Excel文件上传到S3的FILE存储桶, 成功删除本地临时文件");
            }
        }
        return uploadFilePath;
    }

    /**
     * 处理手动激活Excel文件中的数据，根据不同的条件生成激活结果。
     *
     * @param manualActivationExcels 手动激活的Excel文件数据列表。
     * @param uploadActivationExcelMap 已上传的激活Excel文件中车辆VIN与序号的映射。
     * @param needActivationCarVinMap 需要激活的车辆VIN与序号的映射。
     * @param fulfilmentDOMap 需要激活的车辆VIN与序号的映射。
     * @return 返回处理后的手动激活结果Excel文件数据列表。
     */
    @Transactional(rollbackFor = Exception.class)
    public List<ManualActivationResultExcel> processManualActivationExcel(List<AmaPManualActivationExcel> manualActivationExcels,
                                                                          Map<String, Integer> uploadActivationExcelMap,
                                                                          Map<String, Integer> needActivationCarVinMap,
                                                                          Map<String, List<VcsOrderFufilmentDO>> fulfilmentDOMap) {
        List<ManualActivationResultExcel> activationResultExcels = new ArrayList<>();
        for (AmaPManualActivationExcel activationExcel : manualActivationExcels) {
            if (!RENEW_STATUS.equals(activationExcel.getRenewalFeeStatus())) {
                activationResultExcels.add(buildManualActivationResultExcel(activationExcel,
                        activationExcel.getRenewalFeeStatus()));
                continue;
            }
            if (Objects.isNull(uploadActivationExcelMap.get(activationExcel.getCarVin()))) {
                continue;
            }
            if (uploadActivationExcelMap.get(activationExcel.getCarVin())
                    .equals(needActivationCarVinMap.get(activationExcel.getCarVin()))) {
                activationResultExcels.add(buildManualActivationResultExcel(activationExcel,
                        EcpExcelActivationEnum.ECP_SUCCESS.getDesc()));
            } else {
                activationResultExcels.add(buildManualActivationResultExcel(activationExcel,
                        EcpExcelActivationEnum.ECP_FAILED.getDesc()));
            }
        }
        updateActivationFulfilment(uploadActivationExcelMap, needActivationCarVinMap, fulfilmentDOMap);
        return activationResultExcels;
    }

    /**
     * 更新激活履行状态。
     *
     * @param uploadActivationExcelMap 上传的激活Excel表格中车辆VIN码与数量的映射关系。
     * @param needActivationCarVinMap 需要激活的车辆VIN码与数量的映射关系。
     * @param fulfilmentDOMap 订单履行详情的数据对象映射，包含需要更新状态的订单详情。
     */
    public void updateActivationFulfilment(Map<String, Integer> uploadActivationExcelMap,
                                           Map<String, Integer> needActivationCarVinMap,
                                           Map<String, List<VcsOrderFufilmentDO>> fulfilmentDOMap) {
        for (Map.Entry<String, Integer> uploadEntry : uploadActivationExcelMap.entrySet()) {
            if (Objects.isNull(uploadEntry.getValue())) {
                continue;
            }
            if (!uploadEntry.getValue().equals(needActivationCarVinMap.get(uploadEntry.getKey()))) {
                continue;
            }
            //激活成功、进行相应状态的修改
            for (VcsOrderFufilmentDO fulfilmentDO : fulfilmentDOMap.get(uploadEntry.getKey())) {
                activationSuccessUpdateStatus(fulfilmentDO);
            }
        }
    }

    /**
     * 更新激活成功的订单状态。
     * 根据给定的激活Excel表格和订单履行信息，更新相关订单状态和订阅服务信息。
     * @param fulfilmentDO 订单履行详情对象，包含订单履行ID等信息。
     */
    public void activationSuccessUpdateStatus(VcsOrderFufilmentDO fulfilmentDO) {
        String fulfilmentId = fulfilmentDO.getFufilmentId();
        List<VcsOrderFufilmentRecords> fulfilmentRecords = selectNotActiveFulfilmentRecords(List.of(fulfilmentId));
        if (!checkNeedActivationFulfilment(fulfilmentRecords)) {
            log.info("更新激活成功的状态");
            return ;
        }
        updateFulfilmentRecords(fulfilmentRecords);
        updateFulfilmentDO(fulfilmentDO);
        List<SubscriptionServiceDO> subscriptionServiceDOList = getSubscriptionServiceDO(fulfilmentDO.getCarVin());
        // 将record表内过期时间不为空的高德的记录取出来
        VcsOrderFufilmentRecords aMapRecord = fulfilmentRecords.stream().filter(records -> {
            boolean isAMap = ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(records.getServicePackage());
            return isAMap && Objects.nonNull(records.getExpireDate());
        }).findFirst().orElse(null);
        if (CollUtil.isNotEmpty(subscriptionServiceDOList)) {
            insertSubscriptionServiceLog(fulfilmentDO, subscriptionServiceDOList, aMapRecord);
            updateSubscriptionService(subscriptionServiceDOList, fulfilmentDO.getServiceEndDate(), aMapRecord);
        }
    }

    /**
     * 更新订单履行记录中的导航服务激活状态。
     * 此方法遍历给定的订单履行记录列表，如果记录中的服务包名称与连接的导航服务包名称匹配，
     * 则将激活状态设置为成功，并更新记录的更新时间，最后通过mapper接口更新数据库中的记录。
     *
     * @param fulfilmentRecords 订单履行记录列表，包含需要更新的信息。
     */
    private void updateFulfilmentRecords(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (!ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())) {
                continue;
            }
            fulfilmentRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
            fulfilmentRecord.setUpdatedTime(LocalDateTime.now());
            vcsOrderFufilmentRecordsMapper.updateById(fulfilmentRecord);
        }
    }

    /**
     * 更新VC订单履行信息。
     *
     * @param fulfilmentDO 需要更新的订单履行信息对象，包含最新的状态和服务激活状态。
     */
    private void updateFulfilmentDO(VcsOrderFufilmentDO fulfilmentDO) {
        fulfilmentDO.setServiceStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
        fulfilmentDO.setUpdatedTime(LocalDateTime.now());
        vcsOrderFufilmentDOMapper.updateById(fulfilmentDO);
    }

    /**
     * 插入订阅服务日志。
     *
     * @param fulfilmentDO 订阅服务的履行订单数据对象，包含订单的相关信息。
     * @param  serviceDOList 订阅服务列表
     */
    private void insertSubscriptionServiceLog(VcsOrderFufilmentDO fulfilmentDO, List<SubscriptionServiceDO> serviceDOList,
                                              VcsOrderFufilmentRecords aMapRecord) {
        List<SubscriptionServiceLogDO> serviceLogDOS = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            SubscriptionServiceLogDO subscriptionServiceLogDO = SubscriptionServiceLogDO.builder()
                    .subscriptionId(serviceDO.getSubscriptionId())
                    .fufilmentId(fulfilmentDO.getFufilmentId())
                    .refreshBeforeDate(serviceDO.getExpiryDate())
                    .refreshAfterDate(fulfilmentDO.getServiceEndDate())
                    .modifyType(ServiceModifyTypeEnum.AUTO_RENEWAL.getType())
                    .serviceName(serviceDO.getServiceName())
                    .build();
            // 如果aMap记录不为空，且服务包名为高德，则更新高德导航的过期时间
            if (Objects.nonNull(aMapRecord) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                subscriptionServiceLogDO.setRefreshAfterDate(aMapRecord.getExpireDate());
            }
            serviceLogDOS.add(subscriptionServiceLogDO);
        }
        subscriptionServiceLogMapper.insertBatch(serviceLogDOS);
    }

    /**
     * 更新订阅服务信息。
     *
     * @param serviceDOList 订阅服务数据对象列表，包含需要更新的信息。
     * @param refreshAfterDate 更新后的过期日期，所有订阅服务将被设置为这个日期。
     */
    private void updateSubscriptionService(List<SubscriptionServiceDO> serviceDOList, LocalDateTime refreshAfterDate,
                                           VcsOrderFufilmentRecords aMapRecord) {
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            // 如果aMap记录不为空，且服务包名为高德，则更新高德导航的过期时间
            if (Objects.nonNull(aMapRecord) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                serviceDO.setExpiryDate(aMapRecord.getExpireDate());
            } else {
                serviceDO.setExpiryDate(refreshAfterDate);
            }
            serviceDO.setUpdatedTime(LocalDateTime.now());
        }
        subscriptionServiceDOMapper.updateBatch(serviceDOList);
    }


    /**
     * 根据手动激活的Excel表格信息和状态描述，构建手动激活结果的Excel对象。
     *
     * @param activationExcel 手动激活的Excel表格信息，包含车辆VIN、续费状态、结束日期等详细信息。
     * @param statusDesc      手动激活的结果描述，用于表示激活操作的状态，如成功、失败等。
     * @return 返回构建完成的手动激活结果Excel对象，包含所有必要的信息。
     */
    private ManualActivationResultExcel buildManualActivationResultExcel(AmaPManualActivationExcel activationExcel,
                                                                         String statusDesc) {
        return ManualActivationResultExcel.builder()
                .carVin(activationExcel.getCarVin())
                .renewalFeeStatus(activationExcel.getRenewalFeeStatus())
                .endDate(activationExcel.getEndDate())
                .detailInfo(activationExcel.getDetailInfo())
                .operator(activationExcel.getOperator())
                .updater(activationExcel.getUpdater())
                .operationDate(activationExcel.getOperationDate())
                .updateTime(activationExcel.getUpdateTime())
                .renewalOperationId(activationExcel.getRenewalOperationId())
                .renewalAnnual(activationExcel.getRenewalAnnual())
                .amaPOrderId(activationExcel.getAmaPOrderId())
                .ecpActivationResult(statusDesc)
                .build();
    }

    /**
     * 根据VcsOrderFufilmentDO列表获取Excel文件路径列表。
     *
     * @param vcsOrderFufilmentDOList VcsOrderFufilmentDO的列表，包含需要激活的车辆信息。
     * @return 包含生成的Excel文件路径的列表。
     */
    private List<String> getExcelFilePath(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList) {
        log.info("根据VcsOrderFulfilmentDO列表获取Excel文件路径列表, vcsOrderFulfilmentDOList:{}", vcsOrderFufilmentDOList);
        try {
            List<String> excelPathList = new ArrayList<>();
            Map<String, Integer> map = needActivationCarVinMap(vcsOrderFufilmentDOList);
            int count = 1;
            while (!map.isEmpty()) {
                Map<String, Integer> mapCopy = new HashMap<>(map); // 创建map的副本
                List<String> carVinList = new ArrayList<>();
                for (Map.Entry<String, Integer> entry : mapCopy.entrySet()) { // 遍历副本
                    if (entry.getValue() > 0) {
                        carVinList.add(entry.getKey());
                    }
                    int carVinCount = entry.getValue() - 1;
                    map.put(entry.getKey(), carVinCount); // 修改原始map
                    if (carVinCount < 1) {
                        map.remove(entry.getKey()); // 从原始map中移除
                    }
                }
                List<CarVinDownload> carVinDownloadList = buildCarVinDownloadList(carVinList);
                File file = AmaPExcelUtil.writeDownloadCarVinByGroup(carVinDownloadList, count);
                if (Objects.nonNull(file) && StringUtils.isNotBlank(file.getPath())) {
                    excelPathList.add(file.getPath());
                }
                count++;
            }
            return excelPathList;
        } catch (Exception e) {
            // 记录异常信息(需要使用info)
            log.info("根据VcsOrderFulfilmentDO列表获取Excel文件路径列表异常:", e);
        }
        return new ArrayList<>();
    }



    /**
     * 根据车辆VIN列表构建CarVinDownload对象列表。
     *
     * @param carVinList 车辆VIN字符串的列表。这是输入的数据列表，每个字符串代表一个车辆的VIN。
     * @return 返回一个CarVinDownload对象的列表。这个列表中的每个对象都对应于输入列表中的一个VIN，并设置了相应的VIN值。
     */
    private List<CarVinDownload> buildCarVinDownloadList(List<String> carVinList) {
        List<CarVinDownload> carVinDownloadList = new ArrayList<>();
        for (String carVin : carVinList) {
            CarVinDownload carVinDownload = new CarVinDownload();
            carVinDownload.setCarVin(carVin);
            carVinDownloadList.add(carVinDownload);
        }
        return carVinDownloadList;
    }

    /**
     * 统计需要手动激活的车辆VIN码数量。
     *
     * @param uploadExcelList 订单履行对象列表，包含车辆的VIN码信息。
     * @return 返回一个映射，其中键是车辆的VIN码，值是对应VIN码出现的次数。
     */
    private Map<String, Integer> getUploadExcelCarVinCountMap(List<AmaPManualActivationExcel> uploadExcelList) {
        Map<String, Integer> map = new HashMap<>();
        for (AmaPManualActivationExcel activationExcel : uploadExcelList) {
            if (StringUtils.isBlank(activationExcel.getRenewalFeeStatus())
                    || !RENEW_STATUS.equals(activationExcel.getRenewalFeeStatus())) {
                continue;
            }
            Integer carVinCount = map.getOrDefault(activationExcel.getCarVin(), 0);
            carVinCount += 1;
            map.put(activationExcel.getCarVin(), carVinCount);
        }
        return map;
    }

    /**
     * 统计需要激活的车辆VIN码数量。
     *
     * @param vcsOrderFufilmentDOList 订单履行对象列表，包含车辆信息和相关统计数据。
     * @return 一个映射，其键为车辆VIN码，值为对应VIN码需要激活的数量。
     */
    private Map<String, Integer> needActivationCarVinMap(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList) {
        Map<String, Integer> map = new HashMap<>();
        for (VcsOrderFufilmentDO vcsOrderFufilmentDO : vcsOrderFufilmentDOList) {
            int carVinCount = getCarVinCount(vcsOrderFufilmentDO);
            if (carVinCount < 1) {
                continue;
            }
            int value = map.getOrDefault(vcsOrderFufilmentDO.getCarVin(), 0);
            map.put(vcsOrderFufilmentDO.getCarVin(), carVinCount+value);
        }
        return map;
    }


    /**
     * 根据服务开始日期和结束日期，获取车辆VIN码的年份列表。
     *
     * @param vcsOrderFufilmentDO 服务履行信息对象，包含车辆VIN码和服务日期。
     * @return 包含车辆VIN码的年份列表，或者空列表。
     */
    private Integer getCarVinCount(VcsOrderFufilmentDO vcsOrderFufilmentDO) {
        if (Objects.isNull(vcsOrderFufilmentDO)
                || Objects.isNull(vcsOrderFufilmentDO.getServiceEndDate())
                || Objects.isNull(vcsOrderFufilmentDO.getServiceBeginDate())) {
            return 0;
        }
        int startYear = vcsOrderFufilmentDO.getServiceBeginDate().getYear();
        int endYear = vcsOrderFufilmentDO.getServiceEndDate().getYear();
        return endYear - startYear;
    }

    /**
     * 获取需要手动激活的订单履行信息列表。
     *
     * @return 需要手动激活的订单履行信息列表。
     */
    private List<VcsOrderFufilmentDO> getManualActivationFulfilmentDO() {
        List<VcsOrderFufilmentDO> fufilmentDORespList = new ArrayList<>();
        List<VcsOrderFufilmentDO> fulfilmentDOList = selectNotActiveServiceFulfilmentDO();
        log.info("查询未激活的服务订单履行信息:{}", fulfilmentDOList);
        if (CollUtil.isEmpty(fulfilmentDOList)) {
            return fufilmentDORespList;
        }
        List<String> fulfilmentIdList = fulfilmentDOList.stream()
                .map(VcsOrderFufilmentDO::getFufilmentId)
                .collect(Collectors.toList());
        List<VcsOrderFufilmentRecords> fulfilmentRecords = selectNotActiveFulfilmentRecords(fulfilmentIdList);
        log.info("根据履行记录ID列表查询未激活的服务履行记录, fulfilmentIdList:{}, fulfilmentRecords:{}",
                fulfilmentIdList, fulfilmentRecords);
        if (CollUtil.isEmpty(fulfilmentRecords)) {
            return fufilmentDORespList;
        }
        Map<String, List<VcsOrderFufilmentRecords>> fulfilmentRecordMap = getFulfilmentRecordMap(fulfilmentRecords);
        Map<String, VcsOrderFufilmentDO> fulfillmentDOMap = fulfilmentDOList.stream()
                .collect(Collectors.toMap(VcsOrderFufilmentDO::getFufilmentId,
                        Function.identity(),
                        (k1, k2) -> k2
                ));
        log.info("将每个履行ID映射到相应的履行记录列表, fulfillmentDOMap:{}, fulfilmentRecordMap:{}",
                fulfillmentDOMap, fulfilmentRecordMap);
        for (Map.Entry<String, List<VcsOrderFufilmentRecords>> entry : fulfilmentRecordMap.entrySet()) {
            List<VcsOrderFufilmentRecords> fulfilmentRecordList = entry.getValue();
            if (checkNeedActivationFulfilment(fulfilmentRecordList)) {
                fufilmentDORespList.add(fulfillmentDOMap.get(entry.getKey()));
            }
        }
        return fufilmentDORespList;
    }

    /**
     * 检查是否需要满足激活条件。
     *
     * @param fulfilmentRecords 订单履行记录列表，包含每个服务包的激活状态。
     * @return 如果所有服务包都满足激活条件，则返回true；否则返回false。
     */
    private boolean checkNeedActivationFulfilment(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            } else if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())
                    && !ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将订单履行记录列表转换为一个映射，其中每个履行ID映射到相应的履行记录列表
     *
     * @param fulfilmentRecords 履行记录的列表。这个列表中的每个元素都是一个具体的履行记录。
     * @return 返回一个映射，其中每个键是一个履行ID，对应的值是该履行ID的所有履行记录列表。
     */
    private Map<String, List<VcsOrderFufilmentRecords>> getFulfilmentRecordMap(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        Map<String, List<VcsOrderFufilmentRecords>> map = new HashMap<>();
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            List<VcsOrderFufilmentRecords> records = map.getOrDefault(fulfilmentRecord.getFufilmentId(), new ArrayList<>());
            records.add(fulfilmentRecord);
            map.put(fulfilmentRecord.getFufilmentId(), records);
        }
        return map;
    }

    /**
     * 根据车辆识别码和服务套餐获取订阅服务信息。
     *
     * @param carVin 车辆识别码，用于精确查询特定车辆的订阅服务信息。
     * @return 返回匹配条件的订阅服务数据对象列表。
     */
    private List<SubscriptionServiceDO> getSubscriptionServiceDO(String carVin) {
        LambdaQueryWrapperX<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        return subscriptionServiceDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据履行记录ID列表查询服务履行记录。
     *
     * @param fulfilmentIdList 履行记录ID列表。
     * @return 未激活的服务履行记录列表。
     */
    private List<VcsOrderFufilmentRecords> selectNotActiveFulfilmentRecords(List<String> fulfilmentIdList) {
        LambdaQueryWrapperX<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.in(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentIdList)
                .eq(VcsOrderFufilmentRecords::getIsDeleted, false);
        return vcsOrderFufilmentRecordsMapper.selectList(queryWrapper);
    }

    /**
     * 查询未激活的服务订单履行信息
     *
     * @return List<VcsOrderFufilmentDO> 返回符合条件的订单履行信息列表。
     */
    private List<VcsOrderFufilmentDO> selectNotActiveServiceFulfilmentDO() {
        LambdaQueryWrapperX<VcsOrderFufilmentDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(VcsOrderFufilmentDO::getServiceStatus, ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus())
                .eq(VcsOrderFufilmentDO::getServiceType, 2)
                .eq(VcsOrderFufilmentDO::getIsDeleted, false);
        return vcsOrderFufilmentDOMapper.selectList(queryWrapper);
    }
}
