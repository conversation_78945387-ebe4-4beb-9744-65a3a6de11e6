package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fufil.AppDSubscriptionEventEnum;
import com.jlr.ecp.subscription.enums.fufil.CallActivationStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.oss.AppDResultEnum;
import com.jlr.ecp.subscription.enums.oss.RecordResultEnum;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.model.dto.AccessTokenResp;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionReq;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.TimeoutException;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.CALL_APPD_RENEWAL_ERROR;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;

/**
 * APPD续期接口实现
 */
@Service
@Slf4j
public class PIVIAppDServiceImpl implements PIVIAppDService {

    @Resource
    private VcsOrderFulfilmentCallMapper callMapper;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private RestTemplate restTemplateAPPD;
    @Resource
    private RedisService redisService;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private Redisson redisson;
    /**
     * APPD访问令牌 REDIS KEY
     */
    private static final String APPD_ACCESS_TOKEN = "APPD_ACCESS_TOKEN";
    /**
     * REDIS KEY 超时时间
     */
    private static final long TIMEOUT_SECONDS = 600;
    /**
     * APPD 获取TOKEN URL
     */
    @Value("${appd.tokenUrl}")
    private String tokenUrl;
    /**
     * APPD 续期 URL
     */
    @Value("${appd.subUrl}")
    private String subUrl;
    @Value("${appd.getUrl}")
    private String getUrl;
    @Value("${appd.grantType}")
    private String grantType;
    @Value("${appd.clientId}")
    private String clientId;
    @Value("${appd.clientSecret}")
    private String clientSecret;


    /**
     * 调用AppD续期的实现方法。
     *
     * @param message      包含订阅相关信息的消息对象。
     * @param fulfilmentId 履约id。
     * @return boolean 订阅操作是否成功的标志。
     * @ApiLimitation 用于限制该方法的调用频率，使用令牌桶算法，桶名为"appd"，限制每分钟调用次数为50。
     */
    @Override
    public boolean callAppDService(BaseMessage message, String fulfilmentId, Long jlrSubscriptionId) {
        if (Objects.isNull(jlrSubscriptionId)) {
            return true;
        }
        PIVIAppDServiceImpl bean = applicationContext.getBean(getClass());
        // 获取访问令牌
        String accessToken = bean.getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            log.warn("续费消费时accessToken为空. vin={}", message.getVin());
            return false;
        }
        AppDSubscriptionReq request = buildAppDSubscriptionRequest(message, jlrSubscriptionId);
        // 调用restTemplate进行续期
        CommonResult<VcsOrderFufilmentCall> result = concurrentCallAppDRenew(accessToken, request, message.getVin());
        if (!result.isSuccess()) {
            return false;
        }
        VcsOrderFufilmentCall call = result.getData();

        // 写入call表
        if (StringUtils.isNotBlank(fulfilmentId)) {
            addAppDFulfilmentRecord(fulfilmentId, call, message.getTenantId());
        }
        return call.getActivationStatus().equals(CallActivationStatusEnum.SUCCESS.getStatus());
    }

    /**
     * 手动续订APPD
     *
     * @param vin 车辆识别号，用于识别特定的车辆订阅
     * @param endDate 续订的结束日期，用于构建续订请求
     * @return 返回VcsOrderFufilmentCall对象，表示VCS订单履行调用的结果
     */
    @Override
    public CommonResult<VcsOrderFufilmentCall> appDManualRenewal(String vin, LocalDateTime endDate) {
        PIVIPackageDO piviPackageDO = getPIVIPackageByVin(vin);
        log.info("手动续订APPD, vin:{}, piviPackageDO:{}", vin, piviPackageDO);
        if (Objects.isNull(piviPackageDO)) {
            log.info("手动续订APPD, 查询的PIVIPackage为空,vin:{}", vin);
            return CommonResult.error(ErrorCodeConstants.APPDUC_CAR_VIN_EMPTY);
        }
        Long jlrSubscriptionId = piviPackageDO.getJlrSubscriptionId();
        if (Objects.isNull(jlrSubscriptionId)) {
            log.info("手动续订APPD, jlrSubscriptionId为空, vin:{}", vin);
            return CommonResult.error(ErrorCodeConstants.APPDUC_JLR_ID_EMPTY);
        }
        PIVIAppDServiceImpl bean = applicationContext.getBean(getClass());
        // 获取访问令牌
        String accessToken = bean.getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            log.warn("手动续订APPD，续费消费时accessToken为空. vin={}", vin);
            return null;
        }
        AppDSubscriptionReq request = buildAppDManualRequest(endDate, jlrSubscriptionId);
        // 调用restTemplate进行续期
        return concurrentCallAppDRenew(accessToken, request, vin);
    }

    @Override
    public AppDSubscriptionResp getVinSubscriptions(String vin) {
        AppDSubscriptionResp resp = new AppDSubscriptionResp();
        PIVIAppDServiceImpl bean = applicationContext.getBean(getClass());
        // 获取访问令牌
        String accessToken = bean.getAccessToken();
        if (StringUtils.isBlank(accessToken)) {
            log.warn("获取jlrSubscriptionId时accessToken为空. vin={}", vin);
            resp.setQueryResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), "获取jlrSubscriptionId时accessToken为空"));
            resp.setResultCode(AppDResultEnum.REQUEST_ERROR.getCode());
            return resp;
        }
        return getJlrSubscriptionIdRequests(accessToken, vin);
    }

    /**
     * 续费订阅请求处理方法。
     *
     * @param accessToken 访问令牌，用于授权认证。
     * @param request     续费订阅的请求对象，包含所需参数。
     * @return 返回VcsOrderFufilmentCall对象，其中包含请求结果及状态。
     */
    @Retryable(value = {RestClientException.class, TimeoutException.class}, backoff = @Backoff(delay = 1000, multiplier = 2), maxAttempts = 3)
    public VcsOrderFufilmentCall subscriptionRequests(String accessToken, AppDSubscriptionReq request) {
        // 设置header数据
        MultiValueMap<String, String> headerMap = new LinkedMultiValueMap<>();
        headerMap.set("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        headerMap.set("Authorization", "Bearer " + accessToken);
        HttpEntity<AppDSubscriptionReq> entity = new HttpEntity<>(request, headerMap);
        log.info("发送APPD续期请求, request={}", request);
        ResponseEntity<JSONObject> response = restTemplateAPPD.exchange(subUrl, HttpMethod.PUT, entity, JSONObject.class);
        if (Objects.isNull(response.getBody())) {
            throw new RestClientException("response body为空");
        }
        log.info("续费订阅请求处理方法, response:{}", response);
        VcsOrderFufilmentCall fulfilmentCall = new VcsOrderFufilmentCall();
        fulfilmentCall.setRequestParam(JSON.toJSONString(request));
        fulfilmentCall.setRequestResult(response.getBody().toJSONString());
        fulfilmentCall.setActivationStatus(CallActivationStatusEnum.SUCCESS.getStatus());
        return fulfilmentCall;
    }

    /**
     * 续费订阅请求处理方法降级。
     */
    @Recover
    public VcsOrderFufilmentCall subscriptionRequestsFallback(Exception e, String accessToken, AppDSubscriptionReq request) {
        log.error("调用APPD续期失败：{}", e.getMessage());
        VcsOrderFufilmentCall fulfilmentCall = new VcsOrderFufilmentCall();
        fulfilmentCall.setRequestParam(JSON.toJSONString(request));
        JSONObject object = new JSONObject();
        object.put("error", e.getMessage());
        fulfilmentCall.setRequestResult(object.toJSONString());
        fulfilmentCall.setActivationFailedMsg(e.getMessage());
        fulfilmentCall.setActivationStatus(CallActivationStatusEnum.FAILED.getStatus());
        return fulfilmentCall;
    }

    /**
     * 获取APPD的访问令牌。
     *
     * @return 访问令牌字符串，如果无法获取或令牌无效，则返回null。
     */
    @Retryable(value = {RestClientException.class, TimeoutException.class}, backoff = @Backoff(delay = 1000, multiplier = 2), maxAttempts = 3)
    public String getAccessToken() {
        String accessToken = null;
        // 先从redis获取，redis不存在，再去appd获取token
        try {
            accessToken = redisService.getCacheObject(APPD_ACCESS_TOKEN);
        } catch (Exception e) {
            log.error("从redis获取accessToken失败", e);
        }
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        // 设置header数据
        HttpHeaders headers = new HttpHeaders();
        // 参数类型
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 设置参数
        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("grant_type", grantType);
        map.add("client_id", clientId);
        map.add("client_secret", clientSecret);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(map, headers);

        // 发送请求
        ResponseEntity<JSONObject> response = restTemplateAPPD.exchange(tokenUrl, HttpMethod.POST, requestEntity, JSONObject.class);
        if (Objects.isNull(response.getBody())) {
            return null;
        }
        AccessTokenResp accessTokenResp = JSON.parseObject(response.getBody().toString(), AccessTokenResp.class);
        log.info("appd accessTokenResp={}", accessTokenResp);
        accessToken = accessTokenResp.getAccessToken();
        // 存入redis, key的过期时间比返回的token过期时间少600s
        try {
            redisService.setCacheObject(APPD_ACCESS_TOKEN, accessToken, accessTokenResp.getExpiresIn().longValue() - TIMEOUT_SECONDS, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("设置accessToken到redis失败", e);
        }
        return accessToken;
    }

    /**
     * 获取APPD的访问令牌降级。
     *
     * @return 访问令牌字符串，如果无法获取或令牌无效，则返回null。
     */
    @Recover
    public String getAccessTokenFallback(Exception e) {
        log.error("获取appD token失败, e={}", e.getMessage());
        return null;
    }

    /**
     * 根据履行消息构建AppD订阅请求。
     *
     * @param message 履行消息，包含VIN和服务结束日期等信息。
     * @return 构建的AppD订阅请求对象，如果未找到相应的订阅信息，则返回null。
     */
    private AppDSubscriptionReq buildAppDSubscriptionRequest(BaseMessage message, Long jlrSubscriptionId) {
        AppDSubscriptionReq request = new AppDSubscriptionReq();
        request.setSubscriptionEvent(AppDSubscriptionEventEnum.ACTIVATE.getEvent());
        request.setSubscriptionEventDateTime(LocalDateTime.now());
        request.setExpiresDate(message.getServiceEndDate().minusHours(8));
        request.setJlrSubscriptionId(jlrSubscriptionId);
        return request;
    }

    /**
     * 添加APPD履行记录。
     * 该方法用于创建并插入一个应用部署履行记录到数据库中。它详细记录了部署过程中的关键信息，
     *
     * @param fulfilmentId 履行ID
     * @param call         调用记录
     * @param tenantId     租户ID
     */
    private void addAppDFulfilmentRecord(String fulfilmentId, VcsOrderFufilmentCall call, Long tenantId) {
        call.setServicePackage(ServicePackageEnum.ONLINE_PACK.getPackageName());
        call.setServiceName(ServiceNameEnum.APPD.getServiceName());
        call.setFufilmentId(fulfilmentId);
        call.setTenantId(tenantId.intValue());
        callMapper.insert(call);
    }

    /**
     * 查询jlrSubscriptionId请求处理方法。
     *
     * @param accessToken 访问令牌，用于授权认证。
     * @param vin         vin
     */
    public AppDSubscriptionResp getJlrSubscriptionIdRequests(String accessToken, String vin) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set("Authorization", "Bearer " + accessToken);
        HttpEntity<Object> entity = new HttpEntity<>(headers);
        ResponseEntity<List<AppDSubscriptionData>> response;
        AppDSubscriptionResp resp = new AppDSubscriptionResp();
        resp.setResultCode(AppDResultEnum.SYSTEM_ERROR.getCode());
        // 发送请求
        try {
            response = restTemplateAPPD.exchange(getUrl, HttpMethod.GET, entity, new ParameterizedTypeReference<List<AppDSubscriptionData>>() {
            }, vin);
        } catch (Exception e) {
            log.warn("调用APPD查询jlrSubscriptionId失败, vin={}", vin, e);
            resp.setQueryResult(String.format(RecordResultEnum.CALL_ERROR.getDesc(), e.getMessage()));
            return resp;
        }
        log.info("查询jlrSubscriptionId请求处理方法, response:{}", response);
        if (!HttpStatus.OK.equals(response.getStatusCode())) {
            log.warn("调用APPD查询jlrSubscriptionId失败, 返回code不是200, vin={}", vin);
            resp.setQueryResult(String.format(RecordResultEnum.CALL_SUCCESS.getDesc(), JSON.toJSONString(response)));
            if (response.getStatusCode().is4xxClientError()) {
                resp.setResultCode(AppDResultEnum.REQUEST_ERROR.getCode());
            }
            return resp;
        }
        resp.setResultCode(AppDResultEnum.SUCCESS.getCode());
        resp.setResult(response.getBody());
        return resp;
    }


    /**
     * 构建AppD手动订阅请求对象
     *
     * @param endDate         订阅的结束日期时间，用于计算订阅的生效日期
     * @param jlrSubscriptionId 订阅的唯一标识符，用于在系统中识别订阅
     * @return 返回填充了订阅信息的AppDSubscriptionReq对象
     */
    private AppDSubscriptionReq buildAppDManualRequest(LocalDateTime endDate, Long jlrSubscriptionId) {
        AppDSubscriptionReq request = new AppDSubscriptionReq();
        request.setSubscriptionEvent(AppDSubscriptionEventEnum.ACTIVATE.getEvent());
        request.setSubscriptionEventDateTime(LocalDateTime.now());
        request.setExpiresDate(endDate.minusHours(8));
        request.setJlrSubscriptionId(jlrSubscriptionId);
        return request;
    }

    /**
     * 根据车辆识别号（VIN）获取PIVIPackageDO对象
     *
     * @param vin 车辆识别号，用于查询的唯一标识
     * @return 返回包含订阅ID的PIVIPackageDO对象，如果查询结果为空或VIN对应多个结果，则返回null
     */
    private PIVIPackageDO getPIVIPackageByVin(String vin) {
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PIVIPackageDO::getVin, vin)
                .eq(PIVIPackageDO::getIsDeleted, false);
        List<PIVIPackageDO> resp = piviPackageDOMapper.selectList(queryWrapper);
        log.info("根据VIN获取订阅ID, vin:{}, resp:{}", vin, resp);
        if (CollUtil.isEmpty(resp)) {
            log.info("根据VIN获取订阅ID结果为空，vin:{}", vin);
            return null;
        }
        if (resp.size() > 1) {
            log.info("根据VIN获取订阅ID的数量大于1, vin:{}", vin);
        }
        return resp.get(0);
    }

    /**
     * 并发调用AppD续费接口
     * 该方法通过Redis分布式锁机制来控制同一时间只有一个请求在处理AppD续费，以避免并发问题
     *
     * @param accessToken 用户访问令牌，用于调用AppD服务
     * @param request 订阅请求对象，包含续费请求的详细信息
     * @param carVin 车辆VIN号，用于标识车辆和获取锁的唯一键
     * @return 返回AppD续费调用的结果封装在CommonResult中
     */
    public CommonResult<VcsOrderFufilmentCall> concurrentCallAppDRenew(String accessToken, AppDSubscriptionReq request, String carVin) {
        PIVIAppDServiceImpl bean = applicationContext.getBean(getClass());
        // sprint47:并发控制
        RLock lock = redisson.getLock(Constants.REDIS_KEY.CONCURRENT_APPD_RENEWAL_KEY + carVin);
        // 尝试获取锁，不等待
        if (!lock.tryLock()) {
            log.warn("获取AppD续费锁失败, carVin:{}", carVin);
            // 未获取到锁
            return CommonResult.error(ORDER_IN_TRANSIT_ERROR);
        }
        try {
            return CommonResult.success(bean.subscriptionRequests(accessToken, request));
        } catch (Exception e) {
            log.warn("AppD续约请求异常:{}", e.getMessage());
            return CommonResult.error(CALL_APPD_RENEWAL_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock(); // 仅在当前线程持有锁时释放锁
            }
        }
    }
}
