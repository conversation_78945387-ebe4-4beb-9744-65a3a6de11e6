package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderAmapRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderAmapRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.ServiceModifyTypeEnum;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.fufil.AMapQueryOrderStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceActivationStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.model.dto.AmaPChargeSearchResponseDTO;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIFulfilmentService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PIVIFulfilmentServiceImpl implements PIVIFulfilmentService {

    @Resource
    private PIVIAmaPService amaPService;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Resource
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper subscriptionServiceLogMapper;

    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;

    @Resource
    private VcsOrderAmapRecordsMapper vcsOrderAmapRecordsMapper;

    private static final Integer GROUP_COUNT = 10;

    /**
     * 更新PIVI订单履约信息
     *
     * @param vcsOrderCode  车联网订单号，用于标识和查询特定的订单
     * @param fulfilmentId  履约ID，与订单关联，用于精确标识履约信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePIVIOrderFulfilment(String vcsOrderCode, String fulfilmentId) {
        log.info("更新PIVI订单履约信息, vcsOrderCode:{}, fulfilmentId:{}", vcsOrderCode, fulfilmentId);
        AmaPChargeSearchResponseDTO amaPChargeResp = amaPService.queryAmaPChargeInfo(vcsOrderCode);
        log.info("更新PIVI订单履约信息, amaPChargeResp:{}", amaPChargeResp);
        if (Objects.isNull(amaPChargeResp)) {
            return ;
        }
        if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPChargeResp.getCode()) ||
                Objects.isNull(amaPChargeResp.getData()) || CollUtil.isEmpty(amaPChargeResp.getData().getChargeRecords())) {
            log.info("高德的充值结果错误, vcsOrderCode:{}, fulfilmentId:{}", vcsOrderCode, fulfilmentId);
            return ;
        }
        VcsOrderFufilmentDO vcsOrderFufilmentDO = getVcsOrderFufilmentDO(fulfilmentId);
        //更新PIVI激活
        boolean activationResp = activationSuccessUpdateStatus(vcsOrderFufilmentDO,
                amaPChargeResp.getData().getChargeRecords().get(0).getEndTime());
        if (activationResp) {
            //发送PIVI激活通知
            vcsOrderFufilmentDOService.executeOrderStatusSync(vcsOrderFufilmentDO.getOrderCode(),
                    vcsOrderFufilmentDO.getVcsOrderCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePIVIOrderFulfilmentBySixYear(String baseVcsOrderCode, String fulfilmentId) {
        log.info("查询高德续费六年的续费记录, baseVcsOrderCode:{}, fulfilmentId:{}", baseVcsOrderCode, fulfilmentId);
        LocalDateTime maxExpireDate = null;
        String maxExpireDateStr = null;
        for (int i = 1; i <= 2; i++) {
            String vcsOrderCode = baseVcsOrderCode + "-" + i;
            AmaPChargeSearchResponseDTO amaPChargeResp = amaPService.queryAmaPChargeInfo(vcsOrderCode);
            log.info("查询高德续费六年的续费记录结果, amaPChargeResp:{}", amaPChargeResp);
            if (Objects.isNull(amaPChargeResp) || !AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPChargeResp.getCode()) ||
                    Objects.isNull(amaPChargeResp.getData()) || CollUtil.isEmpty(amaPChargeResp.getData().getChargeRecords())) {
                log.info("高德续费六年的充值结果错误, vcsOrderCode:{}, fulfilmentId:{}", vcsOrderCode, fulfilmentId);
                // 更新过程记录表
                updateAMapRecords(fulfilmentId, amaPChargeResp, vcsOrderCode, AMapQueryOrderStatusEnum.FAILURE);
                continue;
            }
            // 更新过程记录表
            updateAMapRecords(fulfilmentId, amaPChargeResp, vcsOrderCode, AMapQueryOrderStatusEnum.SUCCESS);
            String endTimeStr = amaPChargeResp.getData().getChargeRecords().get(0).getEndTime();
            LocalDateTime endTime = SubscribeTimeFormatUtil.stringToTimeByISOZonedDateTimeFormat(endTimeStr);

            if (maxExpireDate == null || (endTime != null && endTime.isAfter(maxExpireDate))) {
                maxExpireDate = endTime;
                maxExpireDateStr = endTimeStr;
            }
        }

        // 检查所有子订单是否都为成功状态
        if (!areAllRecordsSuccessful(fulfilmentId)) {
            log.info("高德续费六年的充值结果不是全部成功, fulfilmentId:{}", fulfilmentId);
            return;
        }
        VcsOrderFufilmentDO vcsOrderFufilmentDO = getVcsOrderFufilmentDO(fulfilmentId);
        //更新PIVI激活
        boolean activationResp = activationSuccessUpdateStatus(vcsOrderFufilmentDO, maxExpireDateStr);
        if (activationResp) {
            //发送PIVI激活通知
            vcsOrderFufilmentDOService.executeOrderStatusSync(vcsOrderFufilmentDO.getOrderCode(),
                    vcsOrderFufilmentDO.getVcsOrderCode());
        }
    }

    /**
     * 更新AMap续费过程记录
     *
     * @param fulfilmentId 履约单ID，用于定位需要更新的记录
     * @param amaPChargeResp AMap充电搜索响应DTO，包含需要更新的充电信息
     * @param orderCode 订单代码，与履约单ID一起用于精确查找记录
     * @param queryOrderStatusEnum 查询订单状态枚举，表示当前订单的状态
     */
    private void updateAMapRecords(String fulfilmentId, AmaPChargeSearchResponseDTO amaPChargeResp,
                                   String orderCode, AMapQueryOrderStatusEnum queryOrderStatusEnum) {
        VcsOrderAmapRecords amapRecords = new VcsOrderAmapRecords();
        amapRecords.setQueryOrderStatus(queryOrderStatusEnum.getStatus());
        amapRecords.setQueryResponse(JSON.toJSONString(amaPChargeResp));
        amapRecords.setUpdatedTime(LocalDateTime.now());
        vcsOrderAmapRecordsMapper.update(amapRecords, new LambdaQueryWrapperX<VcsOrderAmapRecords>()
                .eq(VcsOrderAmapRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderAmapRecords::getVcsOrderCode, orderCode)
                .eq(VcsOrderAmapRecords::getIsDeleted, false)
        );
    }

    /**
     * 判断所有记录是否都成功
     * 该方法用于检查与给定履行ID相关的所有记录是否都具有成功状态
     * 主要用于在执行后续逻辑之前，确保所有相关记录都已成功完成
     *
     * @param fulfilmentId 履行ID，用于查询过程记录表
     * @return 如果所有记录都成功，则返回true；否则返回false
     */
    private boolean areAllRecordsSuccessful(String fulfilmentId) {
        // 查询过程记录表，查询结果均为成功才走后续逻辑
        List<VcsOrderAmapRecords> records = vcsOrderAmapRecordsMapper.selectList(
                new LambdaQueryWrapper<VcsOrderAmapRecords>()
                        .eq(VcsOrderAmapRecords::getFufilmentId, fulfilmentId)
                        .eq(VcsOrderAmapRecords::getIsDeleted, false)
        );
        return records.stream()
                .allMatch(item -> AMapQueryOrderStatusEnum.SUCCESS.getStatus().equals(item.getQueryOrderStatus()));
    }


    @Override
    public void updateOrderAmapRecords(String vcsOrderCode, String fulfilmentId) {
        log.info("查询高德续费六年的续费记录, vcsOrderCode:{}, fulfilmentId:{}", vcsOrderCode, fulfilmentId);
        AmaPChargeSearchResponseDTO amaPChargeResp = amaPService.queryAmaPChargeInfo(vcsOrderCode);
        log.info("查询高德续费六年的续费记录结果, amaPChargeResp:{}", amaPChargeResp);
        if (Objects.isNull(amaPChargeResp) || !AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPChargeResp.getCode()) ||
                Objects.isNull(amaPChargeResp.getData()) || CollUtil.isEmpty(amaPChargeResp.getData().getChargeRecords())) {
            log.info("高德续费六年的充值结果错误, vcsOrderCode:{}, fulfilmentId:{}", vcsOrderCode, fulfilmentId);
            // 更新过程记录表
            updateAMapRecords(fulfilmentId, amaPChargeResp, vcsOrderCode, AMapQueryOrderStatusEnum.FAILURE);
        }else {
            updateAMapRecords(fulfilmentId, amaPChargeResp, vcsOrderCode, AMapQueryOrderStatusEnum.SUCCESS);
        }
    }

    /**
     * 根据VCS订单更新PIVI履行状态
     *
     * @param vcsOrderFulfilmentDOList VCS订单履行信息列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePIVIFulfilmentByVcsOrder(List<VcsOrderFufilmentDO> vcsOrderFulfilmentDOList) {
        log.info("根据VCS订单更新PIVI履行状态, 待处理数量为:{}", vcsOrderFulfilmentDOList.size());
        if (CollUtil.isEmpty(vcsOrderFulfilmentDOList)) {
            return;
        }
        List<List<VcsOrderFufilmentDO>> vcsOrderFulfilmentGroup = getVcsOrderFulfilmentListByGroup(vcsOrderFulfilmentDOList);
        for (List<VcsOrderFufilmentDO> vcsOrderFulfilmentList : vcsOrderFulfilmentGroup) {
            Map<String, AmaPChargeSearchResponseDTO> amaPChargeSearchMap = getAmaPChargeRespInfoMap(vcsOrderFulfilmentDOList);
            if (CollUtil.isEmpty(amaPChargeSearchMap)) {
                log.info("根据VCS订单更新PIVI履行状态,amaPChargeSearchMap为空, vcsOrderFulfilmentList:{}", vcsOrderFulfilmentDOList);
                continue;
            }
            for (VcsOrderFufilmentDO vcsOrderFufilmentDO : vcsOrderFulfilmentList) {
                AmaPChargeSearchResponseDTO amaPChargeResp = amaPChargeSearchMap.get(vcsOrderFufilmentDO.getVcsOrderCode());
                if (Objects.isNull(amaPChargeResp)) {
                    log.info("根据VCS订单更新PIVI履行状态高德的充值结果错误, vcsOrderCode:{}", vcsOrderFufilmentDO.getVcsOrderCode());
                    continue;
                }
                //更新PIVI激活
                if (activationSuccessUpdateStatusByAmaPResp(vcsOrderFufilmentDO, amaPChargeResp)) {
                    //发送PIVI激活通知
                    vcsOrderFufilmentDOService.executeOrderStatusSync(vcsOrderFufilmentDO.getOrderCode(),
                            vcsOrderFufilmentDO.getVcsOrderCode());
                }
            }
        }
    }

    /**
     * 根据高德充值响应更新PIVI订单履约状态为激活成功
     *
     * @param vcsOrderFulfilmentDO PIVI订单履约信息实体
     * @param amaPChargeResp 高德充值响应对象
     */
    public boolean activationSuccessUpdateStatusByAmaPResp(VcsOrderFufilmentDO vcsOrderFulfilmentDO,
                                               AmaPChargeSearchResponseDTO amaPChargeResp) {
        log.info("更新PIVI订单履约信息, amaPChargeResp:{}", amaPChargeResp);
        if (Objects.isNull(amaPChargeResp)) {
            return false;
        }
        if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPChargeResp.getCode()) ||
                Objects.isNull(amaPChargeResp.getData()) || CollUtil.isEmpty(amaPChargeResp.getData().getChargeRecords())) {
            log.info("高德的充值结果错误, ");
            return false;
        }
        return activationSuccessUpdateStatus(vcsOrderFulfilmentDO, amaPChargeResp.getData().getChargeRecords().get(0).getEndTime());
    }

    /**
     * 根据指定的分组数，将订单履行列表划分为多个子列表
     *
     * @param fulfilmentDOList 订单履行数据列表
     * @return 分组后的订单履行列表，每个子列表包含不超过GROUP_COUNT个元素
     */
    private List<List<VcsOrderFufilmentDO>> getVcsOrderFulfilmentListByGroup(List<VcsOrderFufilmentDO> fulfilmentDOList) {
        List<List<VcsOrderFufilmentDO>> vcsOrderFulfilmentDOList = new ArrayList<>();
        if (CollUtil.isEmpty(fulfilmentDOList)) {
            return vcsOrderFulfilmentDOList;
        }
        for (int i = 0; i < fulfilmentDOList.size(); i += GROUP_COUNT) {
            int endIndex = Math.min(i + GROUP_COUNT, fulfilmentDOList.size());
            vcsOrderFulfilmentDOList.add(fulfilmentDOList.subList(i, endIndex));
        }
        return vcsOrderFulfilmentDOList;
    }

    /**
     * 根据订单履行信息列表获取充值响应信息映射
     *
     * @param vcsOrderFulfilmentDOList 订单履行信息列表
     * @return 充值响应信息的映射，键为VCS订单代码
     */
    private Map<String, AmaPChargeSearchResponseDTO> getAmaPChargeRespInfoMap(List<VcsOrderFufilmentDO> vcsOrderFulfilmentDOList) {
        long startTime = System.currentTimeMillis();
        Map<String, CompletableFuture<AmaPChargeSearchResponseDTO>> futureMap = new HashMap<>();
        for (VcsOrderFufilmentDO fulfilmentDO : vcsOrderFulfilmentDOList) {
            CompletableFuture<AmaPChargeSearchResponseDTO> future = CompletableFuture.supplyAsync(() -> {
                return amaPService.queryAmaPChargeInfo(fulfilmentDO.getVcsOrderCode());
            });
            futureMap.put(fulfilmentDO.getVcsOrderCode(), future);
        }
        // 等待所有 CompletableFuture 完成
        Map<String, AmaPChargeSearchResponseDTO> resultMap = futureMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().join() // 使用 join 方法等待 CompletableFuture 完成
                ));

        long endTime = System.currentTimeMillis();
        if (endTime - startTime < 1000) {
            try {
                Thread.sleep(1010 - (endTime - startTime));
            } catch (InterruptedException e) {
                log.info("根据订单履行信息列表获取充值响应信息映射,休眠异常:{}", e.getMessage());
            }
        }
        return resultMap;
    }


    /**
     * 根据履行ID获取订单履行信息
     *
     * @param fulfilmentId 订单履行ID，用于指定要获取的订单履行信息
     * @return 返回找到的订单履行信息对象，如果未找到则可能返回null
     */
    private VcsOrderFufilmentDO getVcsOrderFufilmentDO(String fulfilmentId) {
        return vcsOrderFufilmentDOMapper.getOneByFulfilmentId(fulfilmentId);
    }


    /**
     * 更新激活成功的订单状态。
     * 根据给定的激活的订单履行信息，更新相关订单状态和订阅服务信息。
     * @param fulfilmentDO 订单履行详情对象，包含订单履行ID等信息。
     * @param amaPEndTime 高德充值结束时间，用于计算高德服务到期时间。
     */
    public boolean activationSuccessUpdateStatus(VcsOrderFufilmentDO fulfilmentDO, String amaPEndTime) {
        log.info("更新激活成功的订单状态, fulfilmentDO:{}, amaPEndTime:{}", fulfilmentDO, amaPEndTime);
        if (Objects.isNull(fulfilmentDO)) {
            log.info("更新激活成功的订单状态, fulfilmentDO为空");
            return false;
        }
        //计算高德实际到期时间
        LocalDateTime amaPExpireDate = SubscribeTimeFormatUtil.stringToTimeByISOZonedDateTimeFormat(amaPEndTime);
        List<VcsOrderFufilmentRecords> fulfilmentRecords = selectNotActiveFulfilmentRecords(fulfilmentDO.getFufilmentId());
        updateFulfilmentRecords(fulfilmentRecords, amaPExpireDate);
        if (!checkNeedActivationFulfilment(fulfilmentRecords)) {
            log.info("检查是否需要满足激活条件结果为false, fulfilmentDO:{}", fulfilmentDO);
            return false;
        }
        List<SubscriptionServiceDO> subscriptionServiceDOList = getSubscriptionServiceDO(fulfilmentDO.getCarVin());
        updateFulfilmentDO(fulfilmentDO);
        if (CollUtil.isNotEmpty(subscriptionServiceDOList)) {
            insertSubscriptionServiceLog(fulfilmentDO, subscriptionServiceDOList, amaPExpireDate);
            updateSubscriptionService(subscriptionServiceDOList, fulfilmentDO.getServiceEndDate(), amaPExpireDate);
        }
        return true;
    }

    /**
     * 检查是否需要满足激活条件: AMAP、UNICOM都为激活，APPD为激活或无需激活
     *
     * @param fulfilmentRecords 订单履行记录列表，包含每个服务包的激活状态。
     * @return 如果所有服务包都满足激活条件，则返回true；否则返回false。
     */
    private boolean checkNeedActivationFulfilment(List<VcsOrderFufilmentRecords> fulfilmentRecords) {
        if (CollUtil.isEmpty(fulfilmentRecords)) {
            log.info("检查是否需要满足激活条件,fulfilmentRecords为空");
            return false;
        }
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (ServicePackageEnum.ONLINE_PACK.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    (ServiceActivationStatusEnum.ACTIVATION_UNKNOWN.getStatus().equals(fulfilmentRecord.getActivationStatus())
                            || ServiceActivationStatusEnum.ACTIVATION_FAILED.getStatus().equals(fulfilmentRecord.getActivationStatus()))) {
                log.info("检查是否需要满足激活条件, APPD没有激活, fulfilmentRecord:{}", fulfilmentRecord);
                return false;
            } else if (ServicePackageEnum.DATA_PLAN.getPackageName().equals(fulfilmentRecord.getServicePackage()) &&
                    !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                log.info("检查是否需要满足激活条件, UNICOM没有激活, fulfilmentRecord:{}", fulfilmentRecord);
                return false;
            } else if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())
                    && !ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus().equals(fulfilmentRecord.getActivationStatus())) {
                log.info("检查是否需要满足激活条件, AMAP没有激活, fulfilmentRecord:{}", fulfilmentRecord);
                return false;
            }
        }
        return true;
    }

    /**
     * 插入订阅服务日志。
     *
     * @param fulfilmentDO 订阅服务的履行订单数据对象，包含订单的相关信息。
     * @param  serviceDOList 订阅服务列表
     */
    private void insertSubscriptionServiceLog(VcsOrderFufilmentDO fulfilmentDO, List<SubscriptionServiceDO> serviceDOList,
                                              LocalDateTime amaPExpireDate) {
        List<SubscriptionServiceLogDO> serviceLogDOS = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            SubscriptionServiceLogDO subscriptionServiceLogDO = SubscriptionServiceLogDO.builder()
                    .subscriptionId(serviceDO.getSubscriptionId())
                    .fufilmentId(fulfilmentDO.getFufilmentId())
                    .refreshBeforeDate(serviceDO.getExpiryDate())
                    .refreshAfterDate(fulfilmentDO.getServiceEndDate())
                    .modifyType(ServiceModifyTypeEnum.AUTO_RENEWAL.getType())
                    .serviceName(serviceDO.getServiceName())
                    .build();
            // 如果amaPExpireDate不为空，且服务包名为高德，则更新高德导航的过期时间
            if (Objects.nonNull(amaPExpireDate) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                subscriptionServiceLogDO.setRefreshAfterDate(amaPExpireDate);
            }
            serviceLogDOS.add(subscriptionServiceLogDO);
        }
        subscriptionServiceLogMapper.insertBatch(serviceLogDOS);
    }

    /**
     * 更新订阅服务信息。
     *
     * @param serviceDOList 订阅服务数据对象列表，包含需要更新的信息。
     * @param refreshAfterDate 更新后的过期日期，所有订阅服务将被设置为这个日期。
     * @param amaPExpireDate 高德导航的过期日期，如果存在，则更新高德导航的过期时间。
     */
    private void updateSubscriptionService(List<SubscriptionServiceDO> serviceDOList, LocalDateTime refreshAfterDate,
                                           LocalDateTime amaPExpireDate) {
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            // 如果amaPExpireDate不为空，且服务包名为高德，则更新高德导航的过期时间
            if (Objects.nonNull(amaPExpireDate) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                serviceDO.setExpiryDate(amaPExpireDate);
                serviceDO.setExpireDateUtc0(amaPExpireDate.minusHours(8));
            } else {
                serviceDO.setExpiryDate(refreshAfterDate);
                serviceDO.setExpireDateUtc0(refreshAfterDate.minusHours(8));
            }
            serviceDO.setUpdatedTime(LocalDateTime.now());
        }
        subscriptionServiceDOMapper.updateBatch(serviceDOList);
    }

    /**
     * 根据车辆识别码和服务套餐获取订阅服务信息。
     *
     * @param carVin 车辆识别码，用于精确查询特定车辆的订阅服务信息。
     * @return 返回匹配条件的订阅服务数据对象列表。
     */
    private List<SubscriptionServiceDO> getSubscriptionServiceDO(String carVin) {
        LambdaQueryWrapperX<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        return subscriptionServiceDOMapper.selectList(queryWrapper);
    }

    /**
     * 更新FulfilmentDO履行信息。
     *
     * @param fulfilmentDO 需要更新的订单履行信息对象，包含最新的状态和服务激活状态。
     */
    private void updateFulfilmentDO (VcsOrderFufilmentDO fulfilmentDO){
        log.info("更新FulfilmentDO履行信息, fulfilmentDO:{}", fulfilmentDO);
        fulfilmentDO.setServiceStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
        fulfilmentDO.setUpdatedTime(LocalDateTime.now());
        vcsOrderFufilmentDOMapper.updateById(fulfilmentDO);
    }

    /**
     * 更新订单履行记录中的导航服务激活状态。
     *
     * @param fulfilmentRecords 订单履行记录列表，包含需要更新的信息。
     * @param amaPExpireDate 导航服务到期时间。
     */
    private void updateFulfilmentRecords (List<VcsOrderFufilmentRecords > fulfilmentRecords, LocalDateTime amaPExpireDate) {
        for (VcsOrderFufilmentRecords fulfilmentRecord : fulfilmentRecords) {
            if (!ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(fulfilmentRecord.getServicePackage())) {
                continue;
            }
            //设置AMAP的record到期时间
            fulfilmentRecord.setExpireDate(amaPExpireDate);
            fulfilmentRecord.setActivationStatus(ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus());
            fulfilmentRecord.setUpdatedTime(LocalDateTime.now());
            vcsOrderFufilmentRecordsMapper.updateById(fulfilmentRecord);
        }
    }


    /**
     * 根据履行记录ID列表查询服务履行记录。
     *
     * @param fulfilmentId 履行记录ID列表。
     * @return 未激活的服务履行记录列表。
     */
    private List<VcsOrderFufilmentRecords> selectNotActiveFulfilmentRecords (String fulfilmentId) {
        LambdaQueryWrapperX<VcsOrderFufilmentRecords> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(VcsOrderFufilmentRecords::getFufilmentId, fulfilmentId)
                .eq(VcsOrderFufilmentRecords::getIsDeleted, false);
        return vcsOrderFufilmentRecordsMapper.selectList(queryWrapper);
    }
}
