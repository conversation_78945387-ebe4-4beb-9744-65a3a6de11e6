package com.jlr.ecp.subscription;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;


@SpringBootApplication
@EnableFeignClients("com.jlr.ecp.*")
@EnableAsync
@EnableRetry
@EnableAspectJAutoProxy(exposeProxy = true)
//@SpringBootApplication(exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class})
public class SubscriptionServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SubscriptionServerApplication.class, args);
    }
}
