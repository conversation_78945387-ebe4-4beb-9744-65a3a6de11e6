package com.jlr.ecp.subscription.service.pivi.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderAmapRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderAmapRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.amap.AmaPErrorCode;
import com.jlr.ecp.subscription.enums.amap.AmaPQueryResultEnum;
import com.jlr.ecp.subscription.enums.amap.AmaPServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.*;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentMessage;
import com.jlr.ecp.subscription.model.dto.*;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.properties.AmaPProperties;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.util.SubscribeTimeFormatUtil;
import com.jlr.ecp.subscription.util.pivi.AmaPSignatureGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.common.errors.TimeoutException;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpMethod;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriComponents;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.subscription.constant.Constants.AMAP_RENEWAL_YEAR.*;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.CALL_AMAP_RENEWAL_ERROR;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.ORDER_IN_TRANSIT_ERROR;

@Service
@Slf4j
public class PIVIAmaPServiceImpl implements PIVIAmaPService {
    @Resource
    private AmaPProperties amapProperties;

    @Resource
    private VcsOrderFulfilmentCallMapper recordsMapper;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    private Redisson redisson;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private VcsOrderAmapRecordsMapper vcsOrderAmapRecordsMapper;

    @Resource
    private SubscriptionService subscriptionService;

    private static final String CHARGE_PATH = "/ws/commerce-infra/charge-order/async";

    private static final String CAR_INFO_PATH = "/ws/auth-center/authcar/permission";

    private static final String CHARGE_QUERY_PATH = "/ws/commerce-infra/charge-order";


    /**
     * 调用高德地图充值服务接口。
     *
     * @param fufilmentMessage 履行消息，包含调用高德地图充电服务所需的信息。
     * @param fulfilmentId      履行ID，用于记录调用高德地图充电服务记录。
     * @return boolean 调用成功的标志，true表示成功，false表示失败。
     */
    @Override
    public boolean callAmaPService(FufilmentMessage fufilmentMessage, String fulfilmentId) {
        log.info("调用高德地图充电服务, fulfilmentMessage:{}", fufilmentMessage);
        if (Objects.isNull(fufilmentMessage) || Objects.isNull(fufilmentMessage.getServiceBeginDate())
                || Objects.isNull(fufilmentMessage.getServiceEndDate())) {
            log.info("计算AMAP服务年限为空, fulfilmentMessage:{}", fufilmentMessage);
            return false;
        }
        int year = Math.abs(
                fufilmentMessage.getServiceEndDate().getYear() -
                        fufilmentMessage.getServiceBeginDate().getYear()
        );
        // 续费时长为6年需要做特殊处理
        switch (year) {
            case ONE_YEAR:
            case THREE_YEAR:
                return processRenewal(fufilmentMessage, fulfilmentId, fufilmentMessage.getVcsOrderCode(), year);
            case SIX_YEAR:
                return processSixYearRenewal(fufilmentMessage, fulfilmentId);
            default:
                log.info("计算AMAP服务年限非法, fulfilmentMessage:{}", fufilmentMessage);
                return false;
        }
    }

    /**
     * 处理续费请求
     *
     * @param message 履约信息对象，包含VIN和租户ID等信息
     * @param fulfillmentId 履约ID
     * @param orderCode 订单代码
     * @param year 续费年份
     * @return 如果续费成功返回true，否则返回false
     */
    private boolean processRenewal(FufilmentMessage message, String fulfillmentId,
                                   String orderCode, int year) {
        AmaPOrderChargerRequestDTO requestDTO = buildAmapOrderChargerRequestDTO(message.getVin(), year, orderCode);
        CommonResult<AmaPOrderChargeResponseDTO> result = concurrentCallAMapRenew(requestDTO);

        if (!result.isSuccess() || result.getData() == null) {
            log.warn("调用高德地图充电服务失败, response:{}", result);
            return false;
        }

        AmaPOrderChargeResponseDTO responseDTO = result.getData();
        addAmaPFulfilmentCall(message.getTenantId(), fulfillmentId, requestDTO, responseDTO);

        boolean success = AmaPErrorCode.SUCCESSFUL.getCode().equals(responseDTO.getCode());
        if (!success) {
            log.error("高德地图充电服务返回失败码: {}, 响应: {}", responseDTO.getCode(), responseDTO);
        }
        return success;
    }

    /**
     * 处理六年期高德地图续费服务
     *
     * @param message 履约消息，包含订单相关信息
     * @param fulfillmentId 履约ID，用于查询续费记录
     * @return 续费是否成功，true表示成功，false表示失败
     */
    private boolean processSixYearRenewal(FufilmentMessage message, String fulfillmentId) {
        log.info("调用高德地图续费六年服务, message:{}", message);
        // 查询高德续费过程记录表
        List<VcsOrderAmapRecords> vcsOrderAmapRecords = vcsOrderAmapRecordsMapper.selectList(new LambdaQueryWrapper<VcsOrderAmapRecords>()
                .eq(VcsOrderAmapRecords::getFufilmentId, fulfillmentId)
                .eq(VcsOrderAmapRecords::getIsDeleted, false)
        );
        String baseOrderCode = message.getVcsOrderCode();
        // 过程记录为空需要初始化
        if(CollUtil.isEmpty(vcsOrderAmapRecords)){
            log.info("调用高德地图续费六年服务, 初始化vcsOrderAmapRecords, fulfillmentId:{}", fulfillmentId);
            vcsOrderAmapRecords = initializeOrderRecords(message, fulfillmentId, baseOrderCode);
        }
        Map<String, VcsOrderAmapRecords> vcsOrderMap = vcsOrderAmapRecords.stream().collect(Collectors.toMap(VcsOrderAmapRecords::getVcsOrderCode, Function.identity()));

        boolean renewalSuccess = true;
        for (int i = 1; i <= 2; i++) {
            String vcsOrderCode = baseOrderCode + "-" + i;
            VcsOrderAmapRecords orderAmapRecords = vcsOrderMap.get(vcsOrderCode);
            if(Objects.isNull(orderAmapRecords)){
                log.error("调用高德地图续费六年服务, orderAmapRecords为空, vcsOrderCode:{}", vcsOrderCode);
                return false;
            }
            // 续费成功则无需重复续费
            if (AMapChargeOrderStatusEnum.SUCCESS.getStatus().equals(orderAmapRecords.getChargeOrderStatus())) {
                log.info("调用高德地图续费六年服务, 续费已成功, vcsOrderCode:{}", vcsOrderCode);
                continue;
            }
            // 第一次若续费失败则第二次不做续费
            if (!renewalSuccess) {
                log.info("调用高德地图续费六年服务, 第一次续费失败, fulfillmentId:{}", fulfillmentId);
                return false;
            }
            // 只有待续费状态的记录才去高德续费
            boolean success = processRenewal(message, fulfillmentId, vcsOrderCode, SIX_YEAR);
            updateVcsOrderAmapRecords(orderAmapRecords, success);
            renewalSuccess = success;
        }
        return renewalSuccess;
    }

    /**
     * 初始化订单记录
     * 根据履行消息信息生成并保存VCS订单映射记录
     *
     * @param message 履行消息对象，包含租户ID等信息
     * @param fulfillmentId 履行单ID，用于生成的记录关联
     * @param baseOrderCode 基础订单代码，用于生成VCS订单代码
     * @return 返回生成并保存的VCS订单映射记录列表
     */
    private List<VcsOrderAmapRecords> initializeOrderRecords(FufilmentMessage message, String fulfillmentId, String baseOrderCode) {
        List<VcsOrderAmapRecords> records = new ArrayList<>();
        for (int i = 1; i <= 2; i++) {
            String vcsOrderCode = baseOrderCode + "-" + i;
            records.add(insertVcsOrderAmapRecords(message.getTenantId(), fulfillmentId, vcsOrderCode));
        }
        vcsOrderAmapRecordsMapper.insertBatch(records);
        return records;
    }

    /**
     * 插入高德地图续费过程记录。
     *
     */
    private VcsOrderAmapRecords insertVcsOrderAmapRecords(Long tenantId, String fulfilmentId, String orderCode) {
        VcsOrderAmapRecords amapRecords = new VcsOrderAmapRecords();
        amapRecords.setFufilmentId(fulfilmentId);
        amapRecords.setTenantId(tenantId.intValue());
        amapRecords.setVcsOrderCode(orderCode);
        amapRecords.setChargeOrderStatus(AMapChargeOrderStatusEnum.PRE_CHARGE.getStatus());
        amapRecords.setQueryOrderStatus(AMapQueryOrderStatusEnum.PRE_QUERY.getStatus());
        return amapRecords;
    }

    /**
     * 更新高德地图续费过程记录。
     *
     */
    private void updateVcsOrderAmapRecords(VcsOrderAmapRecords amapRecords, boolean chargeSuccess) {
        amapRecords.setChargeOrderStatus(chargeSuccess ? AMapChargeOrderStatusEnum.SUCCESS.getStatus() : AMapChargeOrderStatusEnum.FAILURE.getStatus());
        amapRecords.setUpdatedTime(LocalDateTime.now());
        vcsOrderAmapRecordsMapper.updateById(amapRecords);
    }

    /**
     * 高德续约
     *
     * @param requestDTO 充电请求的数据载体，包含订单充电的相关信息。
     * @return 返回充电操作的响应结果，如果请求失败或解析异常，返回null。
     */
    @Override
    @ApiLimitation(tokenBucketName = "chargeAmaPOrder")
    @Retryable(value = {RestClientException.class, TimeoutException.class}, backoff = @Backoff(delay = 1000, multiplier = 2), maxAttempts = 3)
    public AmaPOrderChargeResponseDTO chargeAmaPOrder(AmaPOrderChargerRequestDTO requestDTO) {
        log.info("调用高德续约，入参requestDTO:{}", requestDTO);
        String endPoint = amapProperties.getUrl() + CHARGE_PATH;
        Map<String, Object> parameterMap = Maps.newLinkedHashMap();
        parameterMap.put("vid", requestDTO.getVid());
        parameterMap.put("cid", requestDTO.getCid());
        parameterMap.put("cus_order_id", requestDTO.getCusOrderId());
        parameterMap.put("amount", requestDTO.getAmount());
        UriComponents uriComponents = AmaPSignatureGenerator.buildSignature(amapProperties.getAccessKey(),
                amapProperties.getSecretKey(), HttpMethod.POST.name(), endPoint, parameterMap);
        // 将parameterMap转换为JSON字符串
        String requestBodyJson = JSON.toJSONString(parameterMap);
        log.info("高德续约请求参数, requestBodyJson:{}", requestBodyJson);
        // 使用HttpUtil.post发送请求
        String body = HttpUtil.post(uriComponents.toUriString(), requestBodyJson);
        AmaPOrderChargeResponseDTO responseDTO = JSONUtil.toBean(body, AmaPOrderChargeResponseDTO.class);
        log.info("高德续约请求结果, body:{}, responseDTO:{}", body, responseDTO);
        return responseDTO;
    }

    /**
     * 获取高德地图车辆信息
     * @param carVin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    @Override
    public AmaPSearchCenterVO queryAmaPInfo(String carVin) {
        AmaPCarInfoResponse amaPCarInfoResponse = getCarAmaPInfo(amapProperties.getPid(), carVin);
        List<SubscriptionServiceDO> serviceDOList = queryPIVIAmaPServiceDOByCarVin(carVin);
        PIVIPackageDO piviPackageDO = queryPIVIPackageDOByCarVin(carVin);
        IncontrolVehicleDO incontrolVehicleDO = queryVehicleDOByCarVin(carVin);
        log.info("获取AMAP车辆信息, carVin:{}, amaPCarInfoResponse:{}, serviceDOList:{}, piviPackageDO:{}, incontrolVehicleDO:{}",
                carVin, amaPCarInfoResponse, serviceDOList, piviPackageDO, incontrolVehicleDO);
        return buildAmaPSearchCenterVO(amaPCarInfoResponse, serviceDOList, piviPackageDO, incontrolVehicleDO);
    }

    /**
     * 根据车辆识别码（VIN码）查询车辆信息
     *
     * @param carVin 车辆识别码，用于唯一标识一辆汽车
     * @return 返回查询到的车辆信息对象，如果未找到或出现错误则返回null
     */
    private  IncontrolVehicleDO queryVehicleDOByCarVin(String carVin) {
         return incontrolVehicleDOMapper.selectOne(
                new LambdaQueryWrapperX<IncontrolVehicleDO>()
                        .eq(IncontrolVehicleDO::getCarVin, carVin)
                        .eq(IncontrolVehicleDO::getIsDeleted, false)
                        .last("limit 1"));
    }

    /**
     * 获取amap信息
     * @param pid  租户pid
     * @param vin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    @Override
    @ApiLimitation(tokenBucketName = "getCarAmaPInfo")
    public AmaPCarInfoResponse getCarAmaPInfo(String pid, String vin) {
        log.info("获取amap信息, pid:{}, vin:{}", pid, vin);
        String endPoint = amapProperties.getUrl() + CAR_INFO_PATH;
        Map<String, Object> parameterMap = Maps.newLinkedHashMap();
        parameterMap.put("pid", pid);
        parameterMap.put("vid", vin);
        UriComponents uriComponents = AmaPSignatureGenerator.buildSignature(amapProperties.getAccessKey(),
                amapProperties.getSecretKey(), HttpMethod.GET.name(), endPoint, parameterMap);
        String body;
        AmaPCarInfoResponse response = new AmaPCarInfoResponse();
        try {
            log.info("获取amap信息请求参数, uriComponents:{}", uriComponents);
            body = HttpUtil.get(uriComponents.toUriString());
            response = JSONUtil.toBean(body, AmaPCarInfoResponse.class);
            log.info("获取amap信息请求结果, body:{}, response:{}", body, response);
        } catch (Exception e) {
            log.info("获取amap信息异常:{}", e.getMessage());
            response.setQueryResult(e.getMessage());
        }
        return response;
    }

    /**
     * 批量获取汽车amap信息
     * @param pid  租户pid
     * @param vin 车辆识别号
     * @return {@link AmaPCarInfoResponse }<{@link AmaPCarInfoResponse }>
     */
    @Override
    public AmaPCarInfoResponse getCarAmaPInfoBatch(String pid, String vin) {
        log.info("获取amap信息, pid:{}, vin:{}", pid, vin);
        String endPoint = amapProperties.getUrl() + CAR_INFO_PATH;
        Map<String, Object> parameterMap = Maps.newLinkedHashMap();
        parameterMap.put("pid", pid);
        parameterMap.put("vid", vin);
        UriComponents uriComponents = AmaPSignatureGenerator.buildSignature(amapProperties.getAccessKey(),
                amapProperties.getSecretKey(), HttpMethod.GET.name(), endPoint, parameterMap);
        String body;
        AmaPCarInfoResponse response = new AmaPCarInfoResponse();
        try {
            log.info("获取amap信息请求参数, uriComponents:{}", uriComponents);
            body = HttpUtil.get(uriComponents.toUriString());
            response = JSONUtil.toBean(body, AmaPCarInfoResponse.class);
            log.info("获取amap信息请求结果, body:{}, response:{}", body, response);
        } catch (Exception e) {
            log.info("获取amap信息异常:{}", e.getMessage());
        }
        return response;
    }

    /**
     * 查询AMAP充值信息
     *
     * @param cusOrderId 客户订单号，实际就是VcsOrderCode
     * @return 返回支付搜索响应DTO
     */
    @Override
    @ApiLimitation(tokenBucketName = "queryAmaPChargeInfo")
    public AmaPChargeSearchResponseDTO queryAmaPChargeInfo(String cusOrderId) {
        log.info("查询AMAP充值信息, cusOrderId:{}", cusOrderId);
        String endPoint = amapProperties.getUrl() + CHARGE_QUERY_PATH;
        Map<String, Object> parameterMap = Maps.newLinkedHashMap();
        parameterMap.put("cus_order_id", cusOrderId);
        UriComponents uriComponents = AmaPSignatureGenerator.buildSignature(amapProperties.getAccessKey(),
                amapProperties.getSecretKey(), HttpMethod.GET.name(), endPoint, parameterMap);
        AmaPChargeSearchResponseDTO response = null;
        try {
            log.info("查询AMAP充值信息请求入参, uriComponents:{}", uriComponents);
            String body = HttpUtil.get(uriComponents.toUriString());
            response = JSONUtil.toBean(body, AmaPChargeSearchResponseDTO.class);
            log.info("查询AMAP充值信息请求结果, body:{}, response:{}", body, response);
        } catch (Exception e) {
            log.info("查询AMAP充值信息异常:{}", e.getMessage());
        }
        return response;
    }

    @Override
    public AmaPCarInfoResponse queryAmaPExpireDate(String carVin) {
        return getCarAmaPInfo(amapProperties.getPid(), carVin);
    }

    /**
     * 此方法通过聚合多个数据源的信息来丰富AmaPSearchCenterVO对象的属性，为前端提供搜索中心的详细视图。
     *
     * @param amaPCarInfoResponse 汽车信息响应对象，包含汽车相关的详细信息。
     * @param serviceDOList 订阅服务数据对象，包含订阅服务的名称和到期日期等信息。
     * @param piviPackageDO 车辆DMS数据对象，包含车辆的VIN码和发票日期等信息。
     * @return 返回构建完成的AmaPSearchCenterVO对象，包含聚合后的全面信息。
     */
    private AmaPSearchCenterVO buildAmaPSearchCenterVO(AmaPCarInfoResponse amaPCarInfoResponse,
                                                       List<SubscriptionServiceDO> serviceDOList,
                                                       PIVIPackageDO piviPackageDO, IncontrolVehicleDO incontrolVehicleDO) {
        AmaPSearchCenterVO amaPSearchCenterVO = new AmaPSearchCenterVO();
        buildAmaPExpireDateAndStatus(amaPSearchCenterVO, amaPCarInfoResponse);
        buildEcpExpireDate(amaPSearchCenterVO, serviceDOList, piviPackageDO);
        buildCarSystemModel(amaPSearchCenterVO, piviPackageDO, incontrolVehicleDO);
        return amaPSearchCenterVO;
    }

    /**
     * 构建车辆系统的模型信息
     *
     * @param amaPSearchCenterVO 车辆搜索中心视图对象，用于存储车辆系统模型信息
     * @param piviPackageDO 车辆DMS对象，包含车辆系统型号等信息
     * @param incontrolVehicleDO 车辆数据对象，包含车辆系统型号等信息
     */
    private void buildCarSystemModel(AmaPSearchCenterVO amaPSearchCenterVO, PIVIPackageDO piviPackageDO, IncontrolVehicleDO incontrolVehicleDO) {
        if (Objects.isNull(piviPackageDO) && Objects.isNull(incontrolVehicleDO)) {
            return ;
        }
        if (Objects.nonNull(piviPackageDO) && Objects.isNull(incontrolVehicleDO)) {
            amaPSearchCenterVO.setCarSystemModel("PIVI车机");
            return ;
        }
        if (CarSystemModelEnum.PIVI.getCode().equals(incontrolVehicleDO.getCarSystemModel())) {
            amaPSearchCenterVO.setCarSystemModel("PIVI车机");
        } else {
            amaPSearchCenterVO.setCarSystemModel("非PIVI车机");
        }
    }

    /**
     * 根据订阅服务信息，构建服务名称和ECP到期日期
     *
     * @param amaPSearchCenterVO AMA搜索中心视图对象，用于存储服务名称和到期日期。
     * @param serviceDOList 订阅服务数据对象，包含需要提取的服务名称和到期日期信息。
     * @param piviPackageDO 车辆DMS数据对象，包含车辆发票日期信息。
     */
    private void buildEcpExpireDate(AmaPSearchCenterVO amaPSearchCenterVO, List<SubscriptionServiceDO> serviceDOList,
                                    PIVIPackageDO piviPackageDO) {
        log.info("根据订阅服务信息，构建服务名称和ECP到期日期, amaPSearchCenterVO:{}, serviceDOList:{}, piviPackageDO:{}",
                amaPSearchCenterVO, serviceDOList, piviPackageDO);
        if (Objects.isNull(amaPSearchCenterVO)) {
            return ;
        }
        Map<String, LocalDateTime> piviExpireMap = serviceDOList.stream().collect(Collectors.toMap(SubscriptionServiceDO::getServicePackage, SubscriptionServiceDO::getExpiryDate, (e1, e2) -> e1));
        LocalDateTime ecpAppdTime = piviExpireMap.get(ServicePackageEnum.ONLINE_PACK.getPackageName());
        LocalDateTime ecpUnicomTime = piviExpireMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());
        LocalDateTime ecpDisplayTime = ObjectUtil.defaultIfNull(ObjectUtil.defaultIfNull(ecpAppdTime, ecpUnicomTime), subscriptionService.getExpireDateFromAppDCuRenewRecords(piviPackageDO));
        amaPSearchCenterVO.setEcpExpireDate(SubscribeTimeFormatUtil
                .timeToStringByFormat(ecpDisplayTime, SubscribeTimeFormatUtil.FORMAT_1));
    }

    /**
     * 根据车辆信息响应对象构建AmaPSearchCenterVO的AMAP过期日期。
     *
     * @param amaPSearchCenterVO 车辆搜索中心视图对象，用于存储过期日期信息。
     * @param amaPCarInfoResponse 车辆信息响应对象，包含权限信息。
     *                           如果对象为null，或者查询结果无效，或者权限列表为空，则不进行处理。
     */
    private void buildAmaPExpireDateAndStatus(AmaPSearchCenterVO amaPSearchCenterVO,
                                                   AmaPCarInfoResponse amaPCarInfoResponse) {
        if (Objects.isNull(amaPSearchCenterVO) || Objects.isNull(amaPCarInfoResponse)) {
            return ;
        }
        amaPSearchCenterVO.setResultCode(amaPCarInfoResponse.getCode());
        if (!AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPCarInfoResponse.getCode()) || Objects.isNull(amaPCarInfoResponse.getData())
            || CollUtil.isEmpty(amaPCarInfoResponse.getData().getPermissions())) {
            amaPSearchCenterVO.setQueryStatus(AmaPQueryResultEnum.FAIL.getStatus());
            amaPSearchCenterVO.setQueryStatusDesc(AmaPQueryResultEnum.FAIL.getDesc());
            amaPSearchCenterVO.setErrorDesc(getAmaPQueryErrorInfo(amaPCarInfoResponse));
            return ;
        }
        List<AmaPPermissionInfo> pPermissionInfoList = amaPCarInfoResponse.getData().getPermissions();
        amaPSearchCenterVO.setQueryStatus(AmaPQueryResultEnum.SUCCESS.getStatus());
        amaPSearchCenterVO.setQueryStatusDesc(AmaPQueryResultEnum.SUCCESS.getDesc());
        amaPSearchCenterVO.setAmaPExpireDate(SubscribeTimeFormatUtil
                .timeToStringByFormat(pPermissionInfoList.get(0).getEndTime(), SubscribeTimeFormatUtil.FORMAT_1));
        amaPSearchCenterVO.setAmaPServiceStatus(pPermissionInfoList.get(0).getStatus());
        amaPSearchCenterVO.setAmaPServiceStatusDesc(AmaPServiceStatusEnum.getDescByStatus(pPermissionInfoList.get(0).getStatus()));
    }

    /**
     * 根据高德地图服务的响应信息获取错误信息
     *
     * @param amaPCarInfoResponse 高德地图服务返回的车辆信息响应对象
     * @return 如果响应对象为空或表示成功，则返回空字符串；否则，根据错误码返回相应的错误信息
     */
    private String getAmaPQueryErrorInfo(AmaPCarInfoResponse amaPCarInfoResponse) {
        if (Objects.isNull(amaPCarInfoResponse)) {
            return  "";
        }
        if (AmaPErrorCode.SUCCESSFUL.getCode().equals(amaPCarInfoResponse.getCode())) {
            return "";
        }
        return AmaPErrorCode.getManualRenewalDescByCode(amaPCarInfoResponse.getCode());
    }

    /**
     * 根据车辆VIN码查询车辆的发票信息。
     *
     * @param carVin 车辆的唯一标识VIN码。
     * @return 如果找到匹配的车辆信息，则返回VehicleDmsDO对象；如果未找到或VIN码为空，则返回null。
     */
    public PIVIPackageDO queryPIVIPackageDOByCarVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return null;
        }
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PIVIPackageDO::getVin, carVin)
                .eq(PIVIPackageDO::getIsDeleted, false);
        List<PIVIPackageDO> piviPackageDOS = piviPackageDOMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(piviPackageDOS)) {
            return null;
        }
        if (piviPackageDOS.size() > 1) {
            log.warn("根据车辆VIN码查询车辆的发票信息, piviPackageDOS:{}", piviPackageDOS);
        }
        return piviPackageDOS.get(0);
    }


    /**
     * 根据车辆VIN码查询PAMAP服务-ECP到期时间（统一取值appd的到期时间）
     *
     * @param carVin 车辆的唯一标识（VIN码）。
     * @return 返回匹配的SubscriptionServiceDO对象，如果找不到则返回null。
     */
    private List<SubscriptionServiceDO> queryPIVIAmaPServiceDOByCarVin(String carVin) {
        if (StringUtils.isBlank(carVin)) {
            return Collections.emptyList();
        }
        return subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, carVin)
                        .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                        .eq(SubscriptionServiceDO::getIsDeleted, false));
    }


    /**
     * 添加高德地图订单充值记录。
     *
     * @param fulfilmentId 履行ID，用于标识唯一的订单履行记录。
     * @param responseDTO 高德地图订单充电响应DTO，包含了充电结果的详细信息。
     */
    private void addAmaPFulfilmentCall(Long tenantId, String fulfilmentId,
                                       AmaPOrderChargerRequestDTO requestDTO, AmaPOrderChargeResponseDTO responseDTO) {
        VcsOrderFufilmentCall fulfilmentCall = VcsOrderFufilmentCall.builder()
                .fufilmentId(fulfilmentId)
                .servicePackage(ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName())
                .serviceName(ServiceNameEnum.AMAP.getServiceName())
                .requestParam(JSON.toJSONString(requestDTO))
                .requestResult(JSON.toJSONString(responseDTO))
                .activationStatus(getActivationStatus(responseDTO))
                .activationFailedMsg(responseDTO.getErrDetail())
                .tenantId(tenantId.intValue())
                .build();
        recordsMapper.insert(fulfilmentCall);
    }

    /**
     * 根据高德订单充值响应对象获取激活状态码。
     *
     * @param responseDTO 高德订单充值接口的响应对象，包含操作结果信息。
     * @return 激活状态码，根据情况返回不同状态码。
     *         如果响应对象为空，则返回异步处理中的状态码；
     *         如果响应成功，则返回成功状态码；
     *         如果响应失败，则返回失败状态码。
     */
    private Integer getActivationStatus(AmaPOrderChargeResponseDTO responseDTO) {
        if (Boolean.TRUE.equals(responseDTO.getResult())) {
            return CallActivationStatusEnum.SUCCESS.getStatus();
        }
        return CallActivationStatusEnum.FAILED.getStatus();
    }

    /**
     * 根据履行消息构建高德充电订单请求DTO
     *
     */
    private AmaPOrderChargerRequestDTO buildAmapOrderChargerRequestDTO(String vin, Integer year, String orderId) {
        return AmaPOrderChargerRequestDTO.builder()
                .vid(vin)
                .cid(getAmaPCid(year))
                .cusOrderId(orderId)
                .amount(1)
                .build();
    }

    /**
     * 获取AMAP的cid
     *
     * @param amount 金额，用于确定返回哪个CID可能的值包括一年和三年的固定金额
     * @return 根据金额选择的CID如果金额不是预定义的值，则返回空字符串
     */
    private String getAmaPCid(Integer amount) {
        if (ONE_YEAR == amount) {
            return amapProperties.getOneYearCid();
        }
        if (THREE_YEAR == amount || SIX_YEAR == amount) {
            return amapProperties.getThreeYearCid();
        }
        log.info("获取AMAP的cid为空, amount:{}", amount);
        return "";
    }

    /**
     * 计算AMAP服务年限
     *
     * @param fulfilmentMessage 包含服务开始和结束日期的FufilmentMessage对象
     * @return 服务年限，如果输入参数无效则返回0
     */
    private Integer getAmaPAmount(FufilmentMessage fulfilmentMessage) {
        if (Objects.isNull(fulfilmentMessage) || Objects.isNull(fulfilmentMessage.getServiceBeginDate())
                || Objects.isNull(fulfilmentMessage.getServiceEndDate())) {
            log.info("计算AMAP服务年限为空, fulfilmentMessage:{}", fulfilmentMessage);
            return 0;
        }
        int startYear = fulfilmentMessage.getServiceBeginDate().getYear();
        int endYear = fulfilmentMessage.getServiceEndDate().getYear();
        return Math.abs(endYear - startYear);
    }

    /**
     * 并发调用高德续费服务
     * 此方法通过分布式锁机制控制对高德续费接口的并发访问，以避免重复续费
     *
     * @param requestDTO 请求数据传输对象，包含续费请求的必要信息
     * @return 包含高德续费响应结果的通用结果对象
     */
    public CommonResult<AmaPOrderChargeResponseDTO> concurrentCallAMapRenew(AmaPOrderChargerRequestDTO requestDTO) {
        PIVIAmaPServiceImpl bean = applicationContext.getBean(getClass());
        // sprint47:并发控制
        RLock lock = redisson.getLock(Constants.REDIS_KEY.CONCURRENT_AMAP_RENEWAL_KEY + requestDTO.getVid());
        // 尝试获取锁，不等待
        if (!lock.tryLock()) {
            log.warn("获取高德续费锁失败, carVin:{}", requestDTO.getVid());
            // 未获取到锁
            return CommonResult.error(ORDER_IN_TRANSIT_ERROR);
        }
        try {
            return CommonResult.success(bean.chargeAmaPOrder(requestDTO));
        } catch (Exception e) {
            log.warn("高德续约请求异常:{}", e.getMessage());
            return CommonResult.error(CALL_AMAP_RENEWAL_ERROR);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock(); // 仅在当前线程持有锁时释放锁
            }
        }
    }
}
