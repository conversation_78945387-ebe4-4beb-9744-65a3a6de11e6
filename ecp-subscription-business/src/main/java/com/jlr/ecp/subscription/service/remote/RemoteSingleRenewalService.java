package com.jlr.ecp.subscription.service.remote;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.*;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteSinglePageListV0;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;

import java.util.List;

public interface RemoteSingleRenewalService {

    /**
     * remote单个续费操作
     *
     * @param remoteSingleRenewalDTO 续费请求对象，包含续费所需的各项参数
     * @return 返回一个表示操作结果的CommonResult对象，此处返回的是一个字符串类型的CommonResult对象
     */
    CommonResult<String> remoteSingleOperateRenewal(RemoteSingleRenewalDTO remoteSingleRenewalDTO);

    /**
     * 查询单个Remote的分页列表信息
     *
     * @param pageDTO 分页查询参数对象，包含分页及筛选条件
     * @return 返回分页结果对象，包含单页列表和总记录数
     */
    PageResult<RemoteSinglePageListV0> getRemoteSinglePageList(RemoteSingleRenewalPageDTO pageDTO);

    /**
     * 构建REMOTE续费请求参数
     *
     * @param serviceDOList serviceDOList
     * @param renewRecords  renewRecords
     * @return 返回续费请求参数
     */
    OrdFufilmentBusiDTO buildCallApiParam(List<SubscriptionServiceDO> serviceDOList, RemoteRenewDetailRecords renewRecords);

    /**
     * 更新remote续费记录
     *
     * @param renewRecords 应用续费记录对象，包含续费相关数据
     * @param respDTO 接口返回的数据对象，包含续费结果信息
     */
    void updateRemoteRenewRecords(RemoteRenewDetailRecords renewRecords, RemoteModifyRespDTO respDTO);

    /**
     * 远程车控单个续费参数校验
     *
     * @param remoteSingleRenewalDTO 续费请求对象，包含续费所需的各项参数
     * @return 返回一个表示操作结果的CommonResult对象
     */
    CommonResult<RemoteVerifyDTO> verifyParameters(RemoteSingleRenewalDTO remoteSingleRenewalDTO);

    /**
     * 根据车架号检查是否存在相应的车辆处理记录
     * 此方法用于确定是否已经处理过特定车辆，通过车架号进行查询
     *
     * @param carVin 车架号，用于唯一标识一辆车
     * @return Boolean 返回是否存在记录的布尔值
     */
    boolean checkProcessRecord(String carVin);
}
