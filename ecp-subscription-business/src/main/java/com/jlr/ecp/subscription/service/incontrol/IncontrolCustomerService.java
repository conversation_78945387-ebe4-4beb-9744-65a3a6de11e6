package com.jlr.ecp.subscription.service.incontrol;

import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;

import java.util.Set;

/**
 * t_incontrol_customer服务接口
 *
 * <AUTHOR>
 * @since 2023-12-20 14:45:58
 * @description 由 Mybatisplus Code Generator 创建
 */
public interface IncontrolCustomerService {

    /**
     * 主动登录记录到t_incontrol_customer表
     * @param inControlId
     */
    void saveOrUpdateIncontrolCustomerWithProactive(String inControlId);

    /**
     * TSDP过期查询登录记录到t_incontrol_customer表
     * @param customerSet
     */
    void saveOrUpdateIncontrolCustomerWithTSDP(Set<IncontrolCustomerDO> customerSet);
}
