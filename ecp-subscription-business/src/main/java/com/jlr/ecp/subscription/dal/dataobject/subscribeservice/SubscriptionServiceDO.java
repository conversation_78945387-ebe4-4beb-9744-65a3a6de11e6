package com.jlr.ecp.subscription.dal.dataobject.subscribeservice;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_subscription_service(SubscriptionServiceDO)表实体类
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_subscription_service")
public class SubscriptionServiceDO extends BaseDO {

     /**
     * 租户号
     */    
    private Integer tenantId;

     /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 订阅服务ID;订阅服务ID
     */
    private String subscriptionId;

     /**
     * 用户incontrol账号
     */
    private String incontrolId;

    /**
     * 车架号
     */
    private String carVin;

    /**
     * 服务名
     */
    private String serviceName;

    /**
     * 服务包名
     */
    private String servicePackage;

    /**
     * 过期时间
     */
    private LocalDateTime expiryDate;

    /**
     * 过期时间utc0
     */
    @TableField(value = "expire_date_utc0")
    private LocalDateTime expireDateUtc0;

    /**
     * 服务包类型 1：remote vcs服务 2：非remote服务 3: pivi'
     */
    private Integer serviceType;

    /**
     * APPD订阅码
     */
    private Long jlrSubscriptionId;

    /**
     * 联通ICCID
     */
    private String iccid;
}

