package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * t_unicom_check
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_unicom_rnr")
public class UnicomRNRDO {


    /**
    * 主键
    */
    @TableId
    private Long id;

    /**
     * 车辆VIN码;车辆VIN码
     */
    @TableField(value = "vin")
    private String vin;

    /**
    * 车辆VIN码;车辆VIN码
    */
    @TableField(value = "iccid")
    private String iccid;

    /**
     *  发票时间+invoice date+36months
     * */
    @TableField(value = "dms_invoice_date")
    private LocalDateTime dmsInvoiceDate;

    /**
     * 实名状态
     */
    @TableField(value = "real_name_flag")
    private String realNameFlag;

    private String errorMsg;
    /**
     * 租户号
     */
    private Integer tenantId;
}

