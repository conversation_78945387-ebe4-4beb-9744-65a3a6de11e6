package com.jlr.ecp.subscription.service.vehicle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.text.CharSequenceUtil;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.ServicePackageDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.repository.*;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.AppDServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * VIN初始化服务接口实现
 *
 */
@Service
@Slf4j
public class VinInitializeServiceImpl implements VinInitializeService {

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private VehicleDmsRepository vehicleDmsRepository;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;

    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;

    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;

    @Resource
    private PIVIPackageRepository piviPackageRepository;

    @Resource
    private SubscriptionServiceRepository subscriptionServiceRepository;

    @Resource
    private RemotePackageDOService remotePackageDOService;

    @Resource
    private AppDCuRenewRecordsRepository appDCuRenewRecordsRepository;

    @Resource
    private IncontrolCustomerService incontrolCustomerService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource
    private ApplicationContext applicationContext;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vinInitializeByLogin(String inControlId) {
        // 1.通过InControl ID查询TSDP车辆服务信息
        try {
            log.info("用户登录根据inControlId查询TSDP续期服务:{}", inControlId);
            List<VinsAndServiceDTO> infoListFromTSDP = remoteCallService.getByICR(inControlId);
            // 2.保存车辆信息
            // TSDP返回为空更新车辆和ICR的绑定
            if (CollUtil.isEmpty(infoListFromTSDP)) {
                log.info("TSDP未查询到{}的车机数据", inControlId);
                // 将车辆与icr关系表更新, icr字段置为空
                updateInControlIdNull(inControlId);
                return;
            }
            log.info("用户登录根据inControlId查询TSDP续期服务, 初始化车辆信息, inControlId:{}, infoListFromTSDP:{}", inControlId, infoListFromTSDP);
            VinInitializeServiceImpl bean = applicationContext.getBean(getClass());
            bean.vinInitializeByQueryICR(inControlId, infoListFromTSDP);
        } catch (ServiceException | RestClientException serviceException) {
            // 调用第三方的异常需要进行重试
            log.error("用户登录时调用第三方异常, 加入延迟队列重试, inControlId:{}", inControlId);
            remoteCallService.save2DelayQue(inControlId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vinInitializeByQueryICR(String inControlId, List<VinsAndServiceDTO> infoListFromTSDP) {
        // TSDP返回的车辆数据
        List<String> carVinListFromTSDP = infoListFromTSDP.stream().map(VinsAndServiceDTO::getVin).collect(Collectors.toList());
        // 2.1 查询PIVI包信息
        Map<String, PIVIPackageDO> piviPackageDOMap = piviPackageRepository.selectPIVIPackageDOByCarVinList(carVinListFromTSDP)
                .stream().collect(Collectors.toMap(PIVIPackageDO::getVin, p -> p, (old, newP) -> newP));
        log.info("inControlId:{}, piviPackageDOMap:{}", inControlId, piviPackageDOMap);
        // 2.2 保存车辆信息
        buildAndSaveVehicle(inControlId, carVinListFromTSDP, piviPackageDOMap);
        // 3.保存服务信息
        buildAndSaveSubscriptionServices(inControlId, piviPackageDOMap, infoListFromTSDP);
        // 4.保存incontrolCustomer信息
        incontrolCustomerService.saveOrUpdateIncontrolCustomerWithProactive(inControlId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean vinInitializeByQueryVin(String carVin) {
        // 1.根据vin查询TDSP车辆信息
        RemoteQueryByVinRespDTO vinRespDTO = remoteCallService.getByVin(carVin);
        log.info("根据vin查询TDSP车辆信息, vin:{}, response:{}", carVin, vinRespDTO);
        RemoteQueryByVinRespDTO.BoundToCustomer customer = vinRespDTO.getBoundToCustomer();
        String inControlId = null;
        String phone = null;
        String surName = null;
        String firstName = null;
        if(Objects.nonNull(customer)){
            inControlId = Objects.nonNull(customer.getEmail()) ? customer.getEmail().toLowerCase() : null;
            phone = customer.getPhone();
            surName = customer.getSurname();
            firstName = customer.getFirstName();
        }
        Set<String> nameSet = Stream.of(phone, surName, firstName)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, String> encryptListText = piplDataUtil.getEncryptListText(nameSet);
        PIVIPackageDO piviPackageDO = piviPackageRepository.selectOneByCarVin(carVin);
        // 根据vin查询绑定关系
        IncontrolVehicleDO vehicleDO = incontrolVehicleRepository.selectOneByCarVin(carVin);
        String encryptPhone = encryptListText.getOrDefault(phone, "");
        if (vehicleDO == null) {
            log.info("vin={}未在ECP初始化", carVin);
            UserDPResultVO dpResultVO = queryDPByVin(carVin);
            vehicleDO = getInsertVehicleDO(carVin, inControlId, piviPackageDO, encryptPhone, dpResultVO);
            incontrolVehicleRepository.insert(vehicleDO);
        } else {
            log.info("vin={}已初始化", carVin);
            getUpdateVehicleDO(carVin, vehicleDO, inControlId, encryptPhone, piviPackageDO);
            incontrolVehicleRepository.updateBatchWithNulls(List.of(vehicleDO));
        }
        // 新增或更新订阅数据
        saveOrUpdateSubscriptionServices(carVin, inControlId, vinRespDTO, piviPackageDO);
        // 新增、更新 t_incontrol_customer表数据
        incontrolCustomerService.saveOrUpdateIncontrolCustomerByQueryVin(inControlId, encryptListText.getOrDefault(firstName, ""),
                encryptListText.getOrDefault(surName, ""), encryptListText.getOrDefault(phone, ""));
        log.info("byVin保存车辆及服务信息成功, vin:{}", carVin);
        return Objects.isNull(inControlId);
    }

    /**
     * 更新车辆信息对象(IncontrolVehicleDO)的属性值
     *
     * @param carVin 车辆VIN码
     * @param vehicleDO 车辆信息对象，需要被更新的实体对象
     * @param inControlId InControl系统ID
     * @param encryptPhone 加密后的手机号码
     * @param piviPackageDO PIVI包信息对象，用于获取发票日期等信息
     */
    @Override
    public void getUpdateVehicleDO(String carVin, IncontrolVehicleDO vehicleDO, String inControlId, String encryptPhone, PIVIPackageDO piviPackageDO) {
        // vin存在，但是inControlId不相等，则修改inControlId
        String existingICR = vehicleDO.getIncontrolId() != null ? vehicleDO.getIncontrolId().toLowerCase() : null;
        if (!Objects.equals(existingICR, inControlId)) {
            vehicleDO.setIncontrolId(inControlId);
            vehicleDO.setBindTime(LocalDateTime.now());
        }
        vehicleDO.setIncontrolPhone(encryptPhone);
        vehicleDO.setUpdatedTime(LocalDateTime.now());
        // 发票日为空再去更新
        if (Objects.isNull(vehicleDO.getDmsInvoiceDate())) {
            vehicleDO.setDmsInvoiceDate(getDmsInvoiceDate(carVin, piviPackageDO));
        }
        vehicleDO.setRevision(vehicleDO.getRevision() + 1);
    }


    /**
     * 插入车辆信息数据对象
     *
     * @param carVin 车辆识别码
     * @param inControlId 控制ID
     * @param piviPackageDO PIVI包数据对象
     * @param encryptPhone 加密手机号
     * @param dpResultVO 用户DP结果数据对象
     * @return 返回构建好的车辆信息数据对象
     */
    @Override
    public IncontrolVehicleDO getInsertVehicleDO(String carVin, String inControlId, PIVIPackageDO piviPackageDO, String encryptPhone, UserDPResultVO dpResultVO) {
        // sprint52,VIN不存在，调用DP查询车型数据
        // 将dp查询到的新数据都加入到列表中
        seriesBrandMappingDataDOService.createSeriesBrandMappingData(dpResultVO);
        IncontrolVehicleDO vehicleDO = BeanUtil.copyProperties(dpResultVO, IncontrolVehicleDO.class);
        vehicleDO.setIncontrolId(inControlId);
        vehicleDO.setCarVin(carVin);
        vehicleDO.setDmsInvoiceDate(getDmsInvoiceDate(carVin, piviPackageDO));
        vehicleDO.setBindTime(LocalDateTime.now());
        vehicleDO.setIncontrolPhone(encryptPhone);
        return vehicleDO;
    }

    @Override
    public void updateInControlIdNull(String inControlId) {
        // 查询待解绑数据
        List<IncontrolVehicleDO> toUnbindList = incontrolVehicleRepository.selectListByICR(inControlId);
        // 将车辆与icr关系表更新, icr字段置为空
        if (CollUtil.isNotEmpty(toUnbindList)) {
            unbindVehiclesFromUser(toUnbindList);
            incontrolVehicleRepository.updateBatchWithNulls(toUnbindList);
        }
    }


    /**
     * 构建并保存车辆信息
     *
     * @param inControlId ICR账号ID
     * @param carVinListFromTSDP carVinListFromTSDP
     * @param piviPackageDOMap 包含PIVI套餐信息的映射
     */
    public void buildAndSaveVehicle(String inControlId, List<String> carVinListFromTSDP, Map<String, PIVIPackageDO> piviPackageDOMap) {
        // 1. 查询 DMS 发票日期
        Map<String, String> dmsInvoiceMap = buildDmsInvoiceMap(carVinListFromTSDP);

        // 2. 查询 ECP 已有车辆信息
        List<IncontrolVehicleDO> vehicleListFromEcp = incontrolVehicleRepository.selectByCarVinList(carVinListFromTSDP);
        Map<String, IncontrolVehicleDO> existingVehicleMap = toVehicleMap(vehicleListFromEcp);

        // 3. 划分需新增/更新的 VIN
        List<String> needCallDpVinList = getNeedCallDpVinList(carVinListFromTSDP, existingVehicleMap);
        List<IncontrolVehicleDO> needInsertDoList = new ArrayList<>();
        List<IncontrolVehicleDO> needUpdateDoList = new ArrayList<>();

        if (CollUtil.isNotEmpty(needCallDpVinList)) {
            // 4. 调用 DP 获取新 VIN 的数据
            List<UserDPResultVO> dpList = vehicleModelMasterDataService.findDpListByVinInit(needCallDpVinList);

            if (CollUtil.isEmpty(dpList)) {
                log.info("vinList={}未查询到dp数据", needCallDpVinList);
            }

            // 5. 构建需要插入的数据
           needInsertDoList = buildInsertList(inControlId, dpList, piviPackageDOMap, dmsInvoiceMap, needCallDpVinList);
        }

        // 6. 更新已有记录
        updateExistingVehicles(inControlId, existingVehicleMap, piviPackageDOMap, dmsInvoiceMap, needUpdateDoList);

        // 7. 清理不再属于当前用户的数据
        List<IncontrolVehicleDO> vehiclesBoundToUser = incontrolVehicleRepository.selectListByICR(inControlId);
        List<IncontrolVehicleDO> toUnbindList = findVehiclesToUnbind(vehiclesBoundToUser, existingVehicleMap.keySet());

        // 8. 处理解绑并合并更新
        if (CollUtil.isNotEmpty(toUnbindList)) {
            unbindVehiclesFromUser(toUnbindList);
            needUpdateDoList.addAll(toUnbindList);
        }

        // 9. 执行新增和更新
        incontrolVehicleRepository.saveBatch(needInsertDoList);
        incontrolVehicleRepository.updateBatchWithNulls(needUpdateDoList);

        log.info("记录到数据库的Vehicle数量为新增{}条, 更新{}条,inControlId:{}",
                CollUtil.size(needInsertDoList),
                CollUtil.size(needUpdateDoList),
                inControlId);
    }

    /**
     * 根据 VIN 列表查询 DMS 系统中的发票日期，并构建映射表。
     *
     * @param carVinList VIN 列表
     * @return 返回一个 Map，键为 VIN，值为对应的发票日期字符串
     */
    private Map<String, String> buildDmsInvoiceMap(List<String> carVinList) {
        List<VehicleDmsDO> vehicleDmsDOS = vehicleDmsRepository.findByCarVinList(carVinList);
        return vehicleDmsDOS.stream()
                .collect(Collectors.toMap(VehicleDmsDO::getCarVin, VehicleDmsDO::getInvoiceDate, (v1, v2) -> v1));
    }

    /**
     * 解绑不属于当前用户的车辆，即将其 incontrolId 设为 null。
     *
     * @param toUnbindList 需要解绑的车辆列表
     */
    private void unbindVehiclesFromUser(List<IncontrolVehicleDO> toUnbindList) {
        LocalDateTime now = LocalDateTime.now();
        toUnbindList.forEach(v -> {
            v.setIncontrolId(null);
            v.setBindTime(now);
            v.setUpdatedTime(now);
            v.setRevision(v.getRevision() + 1);
        });
    }

    /**
     * 查找当前用户绑定但不在 TSDP 返回列表中的车辆。
     *
     * @param boundList 当前用户绑定的车辆列表
     * @param currentVinSet TSDP 返回的 VIN 集合
     * @return 返回需要解绑的车辆列表
     */
    private List<IncontrolVehicleDO> findVehiclesToUnbind(List<IncontrolVehicleDO> boundList, Set<String> currentVinSet) {
        return boundList.stream()
                .filter(v -> !currentVinSet.contains(v.getCarVin()))
                .collect(Collectors.toList());
    }

    /**
     * 更新已存在的车辆记录，包括：
     * - 更新绑定的 incontrolId；
     * - 更新最后绑定时间；
     * - 更新 DMS 发票日期（如果为空）。
     *
     * @param inControlId 用户的 InControl ID
     * @param existingMap 已存在的车辆映射表
     * @param packageMap PIVI 套餐信息映射表
     * @param dmsMap DMS 发票日期映射表
     * @param needUpdateList 输出参数：需要更新的车辆列表
     */
    private void updateExistingVehicles(String inControlId, Map<String, IncontrolVehicleDO> existingMap,
                                        Map<String, PIVIPackageDO> packageMap, Map<String, String> dmsMap,
                                        List<IncontrolVehicleDO> needUpdateList) {
        existingMap.forEach((vin, vehicle) -> {
            vehicle.setUpdatedTime(LocalDateTime.now());
            vehicle.setRevision(vehicle.getRevision() + 1);
            String existingICR = vehicle.getIncontrolId() != null ? vehicle.getIncontrolId().toLowerCase():null;
            if (!Objects.equals(existingICR, inControlId.toLowerCase())) {
                vehicle.setIncontrolId(inControlId);
                vehicle.setBindTime(LocalDateTime.now());
            }
            // 发票日为空再去更新
            if (Objects.isNull(vehicle.getDmsInvoiceDate())) {
                vehicle.setDmsInvoiceDate(getDmsInvoiceDate(packageMap, vin, dmsMap));
            }
            needUpdateList.add(vehicle);
        });
    }

    /**
     * 获取需要调用 DP 查询的 VIN 列表，即在本地系统中不存在的 VIN。
     *
     * @param vinList VIN 列表
     * @param existingMap 已存在的车辆映射表
     * @return 返回需要调用 DP 查询的 VIN 列表
     */
    private List<String> getNeedCallDpVinList(List<String> vinList, Map<String, IncontrolVehicleDO> existingMap) {
        return vinList.stream()
                .filter(vin -> !existingMap.containsKey(vin))
                .collect(Collectors.toList());
    }

    /**
     * 将车辆 DO 列表转换为以 VIN 为 key 的 Map。
     *
     * @param list 车辆 DO 列表
     * @return 返回以 VIN 为 key 的 Map
     */
    private Map<String, IncontrolVehicleDO> toVehicleMap(List<IncontrolVehicleDO> list) {
        return list.stream()
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, v -> v, (oldOne, newOne) -> newOne));
    }

    /**
     * 构建需要插入的车辆 DO 列表。
     *
     * @param inControlId 用户的 InControl ID
     * @param dpList DP 查询结果 VO 列表
     * @param packageMap PIVI 套餐信息映射表
     * @param dmsMap DMS 发票日期映射表
     * @return 返回需要插入的车辆 DO 列表
     */
    private List<IncontrolVehicleDO> buildInsertList(String inControlId, List<UserDPResultVO> dpList, Map<String, PIVIPackageDO> packageMap,
                                                     Map<String, String> dmsMap, List<String> needCallDpVinList) {
        Map<String, UserDPResultVO> vinMap = dpList.stream()
                .collect(Collectors.toMap(UserDPResultVO::getVin, v -> v, (o, n) -> o));
        // 将dp查询到的新数据都加入到列表中
        return needCallDpVinList.stream().filter(vinMap::containsKey).map(vin -> {
            UserDPResultVO dpResultVO = vinMap.get(vin);
            seriesBrandMappingDataDOService.createSeriesBrandMappingData(dpResultVO);
            IncontrolVehicleDO vehicle = BeanUtil.copyProperties(dpResultVO, IncontrolVehicleDO.class);
            vehicle.setIncontrolId(inControlId.toLowerCase());
            vehicle.setCarVin(vin);
            vehicle.setDmsInvoiceDate(getDmsInvoiceDate(packageMap, vin, dmsMap));
            vehicle.setBindTime(LocalDateTime.now());
            return vehicle;
        }).collect(Collectors.toList());
    }

    /**
     * 获取DMS发票日期
     *
     * @param carVin        车辆识别号
     * @param piviPackageDO PIVI包装对象，可能包含DMS发票日期
     * @return LocalDateTime 返回DMS发票日期
     */
    private LocalDateTime getDmsInvoiceDate(String carVin, PIVIPackageDO piviPackageDO) {
        if (Objects.nonNull(piviPackageDO)) {
            return piviPackageDO.getDmsInvoiceDate();
        }
        // 如果pivi发票日期为null，发票日期要查询dms表
        VehicleDmsDO vehicleDmsDO = vehicleDmsRepository.findByCarVin(carVin);
        if (Objects.isNull(vehicleDmsDO)) {
            return null;
        }
        return TimeFormatUtil.dmsToLocalDate(vehicleDmsDO.getInvoiceDate());
    }

    /**
     * 根据车辆VIN码获取发票日期。
     *
     * @param packageDOMap 车辆信息的映射表，键为VIN码，值为车辆详细信息对象。
     * @param carVin 车辆的VIN码。
     * @return 返回格式化后的发票日期，如果车辆信息映射表为空、VIN码为空或车辆信息中没有发票日期，则返回null。
     */
    private LocalDateTime getDmsInvoiceDate(Map<String, PIVIPackageDO> packageDOMap, String carVin, Map<String, String> dmsMap) {
        if (StringUtils.isBlank(carVin)) {
            return null;
        }
        if (CollUtil.isEmpty(packageDOMap)) {
            if (CollUtil.isEmpty(dmsMap)) {
                return null;
            }
            return TimeFormatUtil.dmsToLocalDate(dmsMap.get(carVin));
        }
        PIVIPackageDO piviPackageDO = packageDOMap.get(carVin);
        if (Objects.nonNull(piviPackageDO)) {
            return piviPackageDO.getDmsInvoiceDate();
        }
        if (CollUtil.isEmpty(dmsMap)) {
            return null;
        }
        return TimeFormatUtil.dmsToLocalDate(dmsMap.get(carVin));
    }

    /**
     * 构建并保存订阅服务信息。
     * 拆分 PIVI 和远程服务后分别处理。
     *
     * @param inControlId 用户的 InControl ID
     * @param piviPackageDOMap PIVI 套餐信息映射表
     * @param infoListFromTSDP TSDP 返回的 VIN 和服务信息列表
     */
    private void buildAndSaveSubscriptionServices(String inControlId, Map<String, PIVIPackageDO> piviPackageDOMap, List<VinsAndServiceDTO> infoListFromTSDP) {
        List<String> carVinListFromTSDP = infoListFromTSDP.stream().map(VinsAndServiceDTO::getVin).collect(Collectors.toList());
        // 查询subscription_service表数据
        List<SubscriptionServiceDO> oldServiceList = subscriptionServiceRepository.selectByCarVinList(carVinListFromTSDP);
        // 拆分PIVI和remote服务
        Map<Boolean, List<SubscriptionServiceDO>> partitionedServices = oldServiceList.stream()
                .collect(Collectors.partitioningBy(service -> Constants.SERVICE_TYPE.PIVI.equals(service.getServiceType())));
        List<SubscriptionServiceDO> onlineServices = partitionedServices.get(true);

        // 保存车辆PIVI订阅服务信息
        buildAndSaveOnlineServices(carVinListFromTSDP, inControlId, piviPackageDOMap, onlineServices);

        List<SubscriptionServiceDO> remoteServices = partitionedServices.get(false);
        // 保存车辆远程服务信息
        buildAndSaveRemoteServices(inControlId, infoListFromTSDP, remoteServices);
    }

    /**
     * 构建并保存远程服务信息。
     * 对比旧服务记录，决定是更新还是插入。
     *
     * @param inControlId 用户的 InControl ID
     * @param vinsAndServiceDTO TSDP 返回的服务信息
     * @param remoteServices 本地已有的远程服务列表
     */
    public void buildAndSaveRemoteServices(String inControlId, List<VinsAndServiceDTO> vinsAndServiceDTO, List<SubscriptionServiceDO> remoteServices) {
        Map<String, Map<String, SubscriptionServiceDO>> groupByVin = remoteServices.stream()
                .collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin, Collectors.toMap(k -> k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v, (k, v) -> k)));

        List<SubscriptionServiceDO> saveOrUpdateList = new ArrayList<>();
        List<String> packageCodes = vinsAndServiceDTO.stream()
                .flatMap(car -> car.getServicePackage().stream())
                .map(ServicePackageDTO::getServicePackageName)
                .collect(Collectors.toList());
        List<String> existPackageCode = remotePackageDOService.getExistPackageCode(packageCodes);
        for (VinsAndServiceDTO andServiceDTO : vinsAndServiceDTO) {
            // 查旧车
            Map<String, SubscriptionServiceDO> oldServiceMap = groupByVin.getOrDefault(andServiceDTO.getVin(), Collections.emptyMap());
            for (ServicePackageDTO servicePackage : andServiceDTO.getServicePackage()) {
                // 到期日为空，则跳过
                if (servicePackage.getExpireDateUTC0() == null) {
                    continue;
                }
                // 比对
                SubscriptionServiceDO oldService = oldServiceMap.get(servicePackage.getServicePackageName() + Constants.DEFAULT_CONCAT_STR + servicePackage.getServiceName());
                if (oldService == null) {
                    SubscriptionServiceDO insertService = buildSubscriptionServiceDO(andServiceDTO.getVin(), servicePackage.getServicePackageName(),
                            servicePackage.getExpireDateUTC0(), servicePackage.getServiceName(), existPackageCode);
                    saveOrUpdateList.add(insertService);
                    // 只有到期日不相等才做更新
                } else if (!servicePackage.getExpireDateUTC0().isEqual(oldService.getExpireDateUtc0())) {
                    oldService.setExpireDateUtc0(servicePackage.getExpireDateUTC0());
                    oldService.setExpiryDate(servicePackage.getExpireDateUTC0().plusHours(8));
                    oldService.setUpdatedTime(LocalDateTime.now());
                    saveOrUpdateList.add(oldService);
                }
            }
        }
        // 同一车下的服务包和服务名称去重
        Map<String, SubscriptionServiceDO> oldServiceMap = saveOrUpdateList.stream().collect(Collectors.toMap(k -> k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v, (v1, v2) -> v1));
        log.info("{}账号下要保存的RemoteSubscriptionService数量为{}条", inControlId, oldServiceMap.size());
        subscriptionServiceRepository.saveOrUpdateBatch(oldServiceMap.values());
    }

    /**
     * 构建并保存在线服务（PIVI）信息。
     * 处理新增 VIN 的订阅服务以及已有服务的更新。
     *
     * @param carVinList TSDP 返回的 VIN 列表
     * @param inControlId 用户的 InControl ID
     * @param piviPackageDOMap PIVI 套餐信息映射表
     * @param onlineServices 本地已有的在线服务列表
     */
    public void buildAndSaveOnlineServices(List<String> carVinList, String inControlId,
                                       Map<String, PIVIPackageDO> piviPackageDOMap, List<SubscriptionServiceDO> onlineServices) {
        log.info("保存Online服务开始，carVinList:{}, inControlId={}", carVinList, inControlId);
        List<SubscriptionServiceDO> saveList = new ArrayList<>();

        List<String> addVinList = getAddVinList(carVinList, onlineServices);
        if (CollUtil.isEmpty(addVinList)) {
            log.info("无新增 VIN 的 PIVI 服务需要保存");
            return;
        }
        log.info("待新增Online服务的vinList:{}", addVinList);
        // 根据vin查询手动续费成功的记录
        Map<String, List<AppDCuRenewRecords>> appDCuRenewRecordsMap = appDCuRenewRecordsRepository.getByVinListAndStatus(addVinList,
                        AppDRenewStatusEnum.RENEW_SUCCESS.getStatus(), null, null)
                .stream().collect(Collectors.groupingBy(AppDCuRenewRecords::getCarVin));
        for (String carVin : addVinList) {
            PIVIPackageDO piviPackageDO = piviPackageDOMap.get(carVin);
            if (Objects.isNull(piviPackageDO)) {
                log.info("根据车辆VIN列表和控制ID批量添加PIVI订阅服务信息, PIVIPackage为空, carVin:{}", carVin);
                continue;
            }
            saveList.addAll(buildPIVISubscriptionServiceDOList(piviPackageDO, inControlId, appDCuRenewRecordsMap.get(carVin)));
        }
        log.info("{}账号下要保存的OnlineSubscriptionService数量为{}条", inControlId, saveList.size());
        subscriptionServiceRepository.saveBatch(saveList);
    }

    /**
     * 构建 SubscriptionServiceDO 对象。
     *
     * @param carVin 车辆识别号
     * @param servicePackage 服务套餐名称
     * @param expireDateUtc0 服务到期时间（UTC 时间）
     * @param serviceName 服务名称
     * @param existPackageCode 存在的远程服务套餐编码集合
     * @return 返回构建完成的 SubscriptionServiceDO 对象
     */
    private SubscriptionServiceDO buildSubscriptionServiceDO(String carVin, String servicePackage,
                                                             LocalDateTime expireDateUtc0, String serviceName, List<String> existPackageCode) {
        SubscriptionServiceDO insertService = new SubscriptionServiceDO();
        insertService.setCarVin(carVin);
        insertService.setServiceName(serviceName);
        insertService.setServicePackage(servicePackage);
        insertService.setExpireDateUtc0(expireDateUtc0);
        insertService.setExpiryDate(expireDateUtc0.plusHours(8));
        insertService.setServiceType(existPackageCode.contains(servicePackage)
                ? Constants.SERVICE_TYPE.REMOTE
                : Constants.SERVICE_TYPE.NOT_REMOTE);
        insertService.setSubscriptionId(ecpIdUtil.nextIdStr());
        return insertService;
    }

    /**
     * 获取需要新增的 VIN 列表。
     * 并将需要更新的订阅服务加入列表。
     *
     * @param carVinList VIN 列表
     * @param serviceDOList 已有的订阅服务列表
     * @return 返回包含需要查询 VIN 列表和需要更新订阅服务列表的 Pair
     */
    private List<String> getAddVinList(List<String> carVinList, List<SubscriptionServiceDO> serviceDOList) {
        if (CollUtil.isEmpty(serviceDOList)) {
            return new ArrayList<>(new HashSet<>(carVinList)); // 去重后返回
        }

        // 提取已有 VIN 的集合
        Set<String> existingVins = serviceDOList.stream()
                .map(SubscriptionServiceDO::getCarVin)
                .collect(Collectors.toSet());

        // 找出不在已有集合中的 VIN
        return carVinList.stream()
                .distinct()
                .filter(carVin -> !existingVins.contains(carVin))
                .collect(Collectors.toList());
    }

    /**
     * 根据PIVI套餐信息和控制ID构建订阅服务数据对象列表。
     *
     * @param piviPackageDO PIVI套餐数据对象列表。
     * @param inControlId    控制ID。
     * @param appDCuRenewRecords    appDCu手动续费记录。
     * @return 返回订阅服务数据对象列表。如果输入的PIVI套餐列表为空，则返回空列表。
     */
    private List<SubscriptionServiceDO> buildPIVISubscriptionServiceDOList(PIVIPackageDO piviPackageDO,
                                                                           String inControlId, List<AppDCuRenewRecords> appDCuRenewRecords) {
        List<SubscriptionServiceDO> piviPackageDOList = new ArrayList<>();
        AppDCuRenewRecords appDRecord = null;
        AppDCuRenewRecords cuRecord = null;
        if (CollUtil.isNotEmpty(appDCuRenewRecords)) {
            // 取appDCuRenewRecords中最新的一条appd的续费记录和cu的续费记录
            appDRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.APPD.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);

            cuRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.UNICOM.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);
        }

        // appD订阅服务到期日取appD手动续费记录的到期日,appD手动续费记录不存在则用pivi_package中的到期日
        LocalDateTime appDExpiryDate = Objects.nonNull(appDRecord) ? appDRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
        // cu订阅服务到期日取cu手动续费记录的到期日,cu手动续费记录不存在则用pivi_package中的到期日
        LocalDateTime cuExpiryDate = Objects.nonNull(cuRecord) ? cuRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
        if (Objects.nonNull(piviPackageDO.getJlrSubscriptionId()) && Objects.nonNull(appDExpiryDate)) {
            Arrays.stream(AppDServiceNameEnum.values())
                    .forEach(service -> piviPackageDOList.add(
                            buildPIVISubscriptionServiceDO(piviPackageDO, inControlId,
                                    service.getServiceName(), appDExpiryDate)
                    ));
        } else {
            log.warn("构建APPD服务数据对象数据为空, appDExpiryDate:{}, piviPackageDO:{}", appDExpiryDate, piviPackageDO);
        }

        if (Objects.isNull(cuExpiryDate)) {
            log.warn("构建订阅服务数据对象, cuExpiryDate为空, piviPackageDO:{}", piviPackageDO);
        } else {
            piviPackageDOList.add(buildPIVISubscriptionDOByPackage(piviPackageDO, inControlId,
                    ServiceNameEnum.UNICOM.getServiceName(), ServicePackageEnum.DATA_PLAN.getPackageName(), cuExpiryDate));
        }

        // aMap直接取pivi_package中的到期日
        if (Objects.isNull(piviPackageDO.getAmaPExpireDate())) {
            log.warn("构建订阅服务数据对象, AmaPExpireDate为空, piviPackageDO:{}", piviPackageDO);
        } else {
            piviPackageDOList.add(buildPIVISubscriptionDOByPackage(piviPackageDO, inControlId,
                    ServiceNameEnum.AMAP.getServiceName(), ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName(), piviPackageDO.getAmaPExpireDate()));
        }
        return piviPackageDOList;
    }

    /**
     * 根据PIVI套餐信息和控制ID构建订阅服务数据对象。
     *
     * @param piviPackageDO PIVI套餐数据对象，包含套餐的详细信息。
     * @param inControlId 控制ID，用于标识订阅服务的控制单元。
     * @param serviceName 服务名称，描述订阅服务的类型或名称。
     * @param servicePackage 服务套餐，具体描述订阅服务的套餐内容或级别。
     * @param expiryDate 服务到期日
     * @return 返回构建完成的SubscriptionServiceDO对象，包含所有的订阅服务信息。
     */
    private SubscriptionServiceDO buildPIVISubscriptionDOByPackage(PIVIPackageDO piviPackageDO, String inControlId,
                                                                   String serviceName, String servicePackage, LocalDateTime expiryDate) {
        log.info("根据PIVI套餐信息和控制ID构建订阅服务数据对象, piviPackageDO:{}, inControlId:{}, serviceName：{}, expiryDate：{}" +
                " servicePackage:{}", piviPackageDO, inControlId, serviceName, servicePackage, expiryDate);
        SubscriptionServiceDO subscriptionService = new SubscriptionServiceDO();
        subscriptionService.setCarVin(piviPackageDO.getVin());
        subscriptionService.setServiceName(serviceName);
        subscriptionService.setIccid(piviPackageDO.getIccid());
        subscriptionService.setSubscriptionId(ecpIdUtil.nextIdStr());
        subscriptionService.setJlrSubscriptionId(piviPackageDO.getJlrSubscriptionId());
        subscriptionService.setServicePackage(servicePackage);
        subscriptionService.setExpiryDate(expiryDate);
        subscriptionService.setExpireDateUtc0(expiryDate.minusHours(8));
        subscriptionService.setServiceType(Constants.SERVICE_TYPE.PIVI);
        return subscriptionService;
    }

    /**
     * 根据PIVI套餐信息和inControl ID构建订阅服务数据对象。
     *
     * @param piviPackageDO PIVI套餐数据对象，包含套餐详情。
     * @param inControlId inControl的ID，用于标识订阅服务的控制器。
     * @param serviceName 服务名称，描述订阅服务的类型或名称。
     * @param expiryDate 服务到期日
     * @return 返回构建完成的订阅服务数据对象。
     */
    private SubscriptionServiceDO buildPIVISubscriptionServiceDO(PIVIPackageDO piviPackageDO, String inControlId,
                                                                 String serviceName, LocalDateTime expiryDate) {
        SubscriptionServiceDO subscriptionService = new SubscriptionServiceDO();
        subscriptionService.setSubscriptionId(ecpIdUtil.nextIdStr());
        subscriptionService.setCarVin(piviPackageDO.getVin());
        subscriptionService.setServiceName(serviceName);
        subscriptionService.setServicePackage(piviPackageDO.getPackageCode());
        subscriptionService.setExpiryDate(expiryDate);
        subscriptionService.setExpireDateUtc0(expiryDate.minusHours(8));
        subscriptionService.setServiceType(Constants.SERVICE_TYPE.PIVI);
        subscriptionService.setJlrSubscriptionId(piviPackageDO.getJlrSubscriptionId());
        subscriptionService.setIccid(piviPackageDO.getIccid());
        return subscriptionService;
    }

    /**
     * 保存或更新订阅服务信息
     * 根据车辆VIN号和新的InControl ID，更新数据库中的订阅服务信息
     * 此方法首先查询数据库中与给定VIN号相关的订阅服务，然后根据新的InControl ID更新PIVI服务的绑定关系
     * 接着，根据从远程查询响应中获取的订阅信息，更新或插入订阅服务记录
     * 最后，去重并批量保存或更新订阅服务信息
     *
     * @param carVin 车辆VIN号，用于查询和更新订阅服务
     * @param inControlId 新的InControl ID，用于更新订阅服务的绑定关系
     * @param vinRespDTO 远程查询响应对象，包含车辆的订阅信息
     */
    private void saveOrUpdateSubscriptionServices(String carVin, String inControlId, RemoteQueryByVinRespDTO vinRespDTO, PIVIPackageDO piviPackageDO) {
        // 查询subscription_service表数据
        List<SubscriptionServiceDO> oldServiceList = subscriptionServiceRepository.selectByCarVin(carVin);
        List<SubscriptionServiceDO> saveOrUpdateList = new ArrayList<>();
        // 拆分PIVI和remote服务
        Map<Boolean, List<SubscriptionServiceDO>> partitionedServices = oldServiceList.stream()
                .collect(Collectors.partitioningBy(service -> Constants.SERVICE_TYPE.PIVI.equals(service.getServiceType())));
        List<SubscriptionServiceDO> onlineServices = partitionedServices.get(true);
        // 保存车辆PIVI订阅服务信息
        if (CollUtil.isEmpty(onlineServices) && Objects.nonNull(piviPackageDO)) {
            // 根据vin查询手动续费成功的记录
            List<AppDCuRenewRecords> appDCuRenewRecords = appDCuRenewRecordsRepository.getByVinAndStatus(carVin, AppDRenewStatusEnum.RENEW_SUCCESS.getStatus());
            saveOrUpdateList.addAll(buildPIVISubscriptionServiceDOList(piviPackageDO, inControlId, appDCuRenewRecords));
        }

        List<SubscriptionServiceDO> remoteServices = partitionedServices.get(false);
        Map<String, SubscriptionServiceDO> oldServiceMap = remoteServices.stream()
                .collect(Collectors.toMap(k -> k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v));
        List<RemoteQueryByVinRespDTO.Subscription> subscriptions = vinRespDTO.getSubscriptions();
        List<String> packageCodes = subscriptions.stream()
                .map(RemoteQueryByVinRespDTO.Subscription::getServicePackage)
                .collect(Collectors.toList());
        List<String> existPackageCode = remotePackageDOService.getExistPackageCode(packageCodes);
        // 更新remote服务
        for (RemoteQueryByVinRespDTO.Subscription subscription : subscriptions) {
            // 到期日为空，则跳过
            if (subscription.getExpiryDate() == null) {
                continue;
            }
            for (String service : subscription.getServices()) {
                // 比对
                SubscriptionServiceDO oldService = oldServiceMap.get(subscription.getServicePackage() + Constants.DEFAULT_CONCAT_STR + service);
                if (oldService == null) {
                    SubscriptionServiceDO insertService = buildSubscriptionServiceDO(carVin, subscription.getServicePackage(),
                            subscription.getExpiryDate(), service, existPackageCode);
                    saveOrUpdateList.add(insertService);
                    // 只有到期日不相等才做更新
                } else if (!subscription.getExpiryDate().isEqual(oldService.getExpireDateUtc0())) {
                    oldService.setExpireDateUtc0(subscription.getExpiryDate());
                    oldService.setExpiryDate(subscription.getExpiryDate().plusHours(8));
                    oldService.setUpdatedTime(LocalDateTime.now());
                    saveOrUpdateList.add(oldService);
                }
            }
        }
        log.info("{}账号下要保存或更新的subscriptionService数量为{}条", inControlId, saveOrUpdateList.size());
        subscriptionServiceRepository.saveOrUpdateBatch(saveOrUpdateList);
    }

    /**
     * 根据车辆识别号（VIN）查询DP信息
     * 该方法用于代客下单过程中，通过车辆的VIN来获取DP（Dealer Principle）信息
     * 如果查询结果中包含错误代码，则抛出异常；如果配置代码为空，则认为车辆信息不存在，同样抛出异常
     *
     * @param carVin 车辆识别号（VIN），用于查询DP信息
     * @return 返回包含DP信息的UserDPResultVO对象
     */
    private UserDPResultVO queryDPByVin(String carVin) {
        UserDPResultVO resultVO = vehicleModelMasterDataService.findDp(carVin);
        log.info("byVin获取DP信息, vin:{}, response:{}", carVin, resultVO);
        if (CharSequenceUtil.isNotBlank(resultVO.getQueryResult())) {
            log.error("byVin调用dp服务异常, vin:{}", carVin);
            throw exception(ErrorCodeConstants.CALL_DP_ERROR);
        }
        if (CharSequenceUtil.isBlank(resultVO.getConfigCode())) {
            log.info("byVin获取DP信息为空, vin:{}", carVin);
            throw exception(ErrorCodeConstants.CALL_DP_EMPTY);
        }
        log.info("byVin获取DP信息成功, vin:{}", carVin);
        return resultVO;
    }
}
