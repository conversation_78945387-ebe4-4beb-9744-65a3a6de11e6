package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireVo;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.SubscriptionServiceRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 订阅服务Repository实现类
 */
@Component
@Slf4j
public class SubscriptionServiceRepositoryImpl extends ServiceImpl<SubscriptionServiceMapper, SubscriptionServiceDO> implements SubscriptionServiceRepository {

    @Override
    public List<SubscriptionServiceDO> queryExpireService(LocalDateTime startDate, LocalDateTime endDate) {
        log.info("查询过期服务, startDate: {}, endDate: {}", startDate, endDate);
        return baseMapper.queryExpireService(startDate, endDate);
    }

    @Override
    public List<SubscriptionServiceDO> queryByCarVinAndServiceType(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        log.info("根据车辆VIN和服务类型查询, size: {}", carVinAndServiceTypeList.size());
        return baseMapper.queryByCarVinAndServiceType(carVinAndServiceTypeList);
    }

    @Override
    public List<SubscriptionServiceDO> getLatestExpireDate(Set<String> carVinSet) {
        log.info("获取最新过期日期, carVinSet size: {}", carVinSet.size());
        return baseMapper.getLatestExpireDate(carVinSet);
    }

    @Override
    public List<SubscriptionServiceDO> getLatestUnicomExpireDate(Set<String> carVinSet) {
        log.info("获取最新联通过期日期, carVinSet size: {}", carVinSet.size());
        return baseMapper.getLatestUnicomExpireDate(carVinSet);
    }

    @Override
    public SubscriptionServiceDO findICCIDByCarVin(String carVin) {
        log.info("根据车辆VIN查找ICCID, carVin: {}", carVin);
        return baseMapper.findICCIDByCarVin(carVin);
    }

    @Override
    public List<SubscriptionServiceDO> queryServiceDOByCarVinAndPackage(String carVin, String packageName) {
        log.info("根据车辆VIN和包名查询服务, carVin: {}, packageName: {}", carVin, packageName);
        if (StringUtils.isBlank(carVin)) {
            return Collections.emptyList();
        }
        return list(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServicePackage, packageName)
                .eq(SubscriptionServiceDO::getIsDeleted, false));
    }

    @Override
    public List<ServiceExpireVo> queryCombinedExpireList(LocalDateTime startTime, LocalDateTime endTime,
                                                         Integer serviceType, String servicePackage,
                                                         Integer pageSize, Integer offset) {
        log.info("查询组合过期列表, startTime: {}, endTime: {}, serviceType: {}, servicePackage: {}, pageSize: {}, offset: {}",
                startTime, endTime, serviceType, servicePackage, pageSize, offset);
        return baseMapper.queryCombinedExpireList(startTime, endTime, serviceType, servicePackage, pageSize, offset);
    }

    @Override
    public Integer queryCombinedExpireCount(LocalDateTime startTime, LocalDateTime endTime,
                                            Integer serviceType, String servicePackage) {
        log.info("查询组合过期数量, startTime: {}, endTime: {}, serviceType: {}, servicePackage: {}",
                startTime, endTime, serviceType, servicePackage);
        return baseMapper.queryCombinedExpireCount(startTime, endTime, serviceType, servicePackage);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<SubscriptionServiceDO> entities) {
        log.info("批量保存或更新订阅服务, size: {}", entities.size());
        return super.saveOrUpdateBatch(entities);
    }

    @Override
    public List<SubscriptionServiceDO> selectByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表查询订阅服务, carVinList size: {}", carVinList.size());
        return list(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(BaseDO::getIsDeleted, false));
    }

    @Override
    public List<SubscriptionServiceDO> selectByCarVin(String carVin) {
        log.info("根据车辆VIN查询订阅服务, carVin: {}", carVin);
        return list(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(BaseDO::getIsDeleted, false));
    }

    @Override
    public List<SubscriptionServiceDO> findByVinSetAndServiceType(Set<String> vinSet, Integer serviceType) {
        if (vinSet == null || vinSet.isEmpty()) {
            log.warn("VIN集合为空，返回空列表");
            return Collections.emptyList();
        }

        log.info("根据VIN集合和服务类型查询订阅服务, vinSet size: {}, serviceType: {}", vinSet.size(), serviceType);
        return list(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, vinSet)
                .eq(SubscriptionServiceDO::getServiceType, serviceType)
                .eq(BaseDO::getIsDeleted, false));
    }
}
