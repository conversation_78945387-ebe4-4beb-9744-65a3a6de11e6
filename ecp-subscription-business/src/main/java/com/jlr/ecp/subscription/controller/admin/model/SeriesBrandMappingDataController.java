package com.jlr.ecp.subscription.controller.admin.model;


import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesBrandMappingDataPageVO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingDataUpdateDTO;
import com.jlr.ecp.subscription.controller.admin.dto.seriesMapping.SeriesMappingQueryPageDTO;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.series.SeriesBrandEnum;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "平台管理 - 车型展示名称配置")
@RestController
@RequestMapping("v1/series/mapping")
@Validated
public class SeriesBrandMappingDataController {


    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;


    /**
     * 车型展示名称配置分页查询API接口
     * @param pageDTO 分页入参
     * @return PageResult<SeriesBrandMappingDataPageVO>
     */
    @PostMapping("/page")
    @Operation(summary = "分页查询")
    @PreAuthorize("@ss.hasPermission('platform:vehicle-model:list')")
    public CommonResult<PageResult<SeriesBrandMappingDataPageVO>> query(@RequestBody @Valid SeriesMappingQueryPageDTO pageDTO) {
        PageResult<SeriesBrandMappingDataPageVO> resp = seriesBrandMappingDataDOService.queryPageList(pageDTO);
        return CommonResult.success(resp);
    }

    /**
     * 编辑车型展示名称配置API接口
     * @param updateDTO 编辑入参
     * @return String
     */
    @PutMapping( "/edit")
    @Operation(summary = "编辑车型展示名称配置")
    @PreAuthorize("@ss.hasPermission('platform:vehicle-model:edit')")
    CommonResult<String> editSeriesBrandMappingData(@Validated @RequestBody SeriesMappingDataUpdateDTO updateDTO){
        Boolean success = seriesBrandMappingDataDOService.editSeriesBrandMappingData(updateDTO);
        if(success){
            return CommonResult.success(Constants.SERIES_MAPPING_UPDATE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_MAPPING_UPDATE_FAIL);
    }



    /**
     * 删除车型展示名称
     * @param id 车型编码
     * @return CommonResult<String>
     */
    @DeleteMapping( "/delete")
    @Operation(summary = "删除车型展示名称")
    @Parameter(name = "id", description = "id", required = true)
    @Parameter(name = "revision", description = "版本号", required = true)
    @PreAuthorize("@ss.hasPermission('platform:vehicle-model:delete')")
    CommonResult<String> delete(@RequestParam("id") Long id,@RequestParam("revision") Integer revision){
        Boolean success = seriesBrandMappingDataDOService.delete(id,revision);
        if(success){
            return CommonResult.success(Constants.SERIES_MAPPING_DELETE_SUCCESS_MESSAGE);
        }
        return CommonResult.error(ErrorCodeConstants.SERIES_MAPPING_DELETE_FAIL);
    }




    @GetMapping( "/hint")
    @Operation(summary = "提醒未填写配置接口")
    @PreAuthorize("@ss.hasPermission('platform:applicable:edit')") //和前端约定的特殊配置
    CommonResult<Long> hintBubble(){
        Long num = seriesBrandMappingDataDOService.getNoCompleteCount();
        return CommonResult.success(num);
    }



    /**
     * 获取所有枚举常量的 displayName 列表
     *
     * @return 包含所有 displayName 的 List<String>
     */
    @GetMapping( "/list")
    @Operation(summary = "获取车型展示名称配置列表")
    @PreAuthorize("@ss.hasPermission('platform:vehicle-model:list')")
    CommonResult<List<String>> list(){
        List<String> displayNames = new ArrayList<>();
        for (SeriesBrandEnum brand : SeriesBrandEnum.values()) {
            displayNames.add(brand.getDisplayName());
        }
        return CommonResult.success(displayNames);
    }






}
