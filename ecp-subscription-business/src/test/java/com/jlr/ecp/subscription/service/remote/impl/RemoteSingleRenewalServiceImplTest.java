package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.lang.Snowflake;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.common.pojo.PageResult;
import com.jlr.ecp.subscription.controller.admin.dto.remote.*;
import com.jlr.ecp.subscription.controller.admin.vo.remote.RemoteSinglePageListV0;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.remote.RemoteRenewDetailRecordsMapper;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.service.manuallog.ManualModifyLogDOService;
import com.jlr.ecp.subscription.service.order.OrderCheckService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RemoteSingleRenewalServiceImplTest {

    @Mock
    private Snowflake mockSnowflake;
    @Mock
    private RemoteCallService mockRemoteCallService;
    @Mock
    private SubscriptionService mockSubscriptionService;
    @Mock
    private RemoteRenewDetailRecordsMapper mockRemoteRenewDetailRecordsMapper;
    @Mock
    private ApplicationContext mockApplicationContext;
    @Mock
    private ManualModifyLogDOService mockManualModifyLogDOService;
    @Mock
    private OrderCheckService mockOrderCheckService;

    @InjectMocks
    private RemoteSingleRenewalServiceImpl remoteSingleRenewalServiceImplUnderTest;

    @Test
    public void testRemoteSingleOperateRenewal() {
        // Setup
        final RemoteSingleRenewalDTO remoteSingleRenewalDTO = new RemoteSingleRenewalDTO();
        remoteSingleRenewalDTO.setCarVin("qweqwe12345678900");
        remoteSingleRenewalDTO.setExpireDate("3020/01/01");

        final CommonResult<String> expectedResult = CommonResult.success("请求已发送，请前往续费记录查看结果");

        // Configure SubscriptionService.getRemoteExpireDateByVin(...).
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("qweqwe12345678900");
        resultDTO.setExistInEcp(true);
        resultDTO.setPiviModel(true);
        resultDTO.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setServiceDOList(List.of(SubscriptionServiceDO.builder()
                .incontrolId("incontrolId")
                .carVin("qweqwe12345678900")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .build()));
        when(mockSubscriptionService.getRemoteExpireDateByVin("qweqwe12345678900")).thenReturn(resultDTO);
        when(mockRemoteRenewDetailRecordsMapper.getProcessRecordsByVin("qweqwe12345678900")).thenReturn(Collections.emptyList());

        when(mockOrderCheckService.checkOrderInTransit("qweqwe12345678900", ServiceTypeEnum.REMOTE))
                .thenReturn(CommonResult.success("qweqwe12345678900"));
        when(mockSnowflake.nextId()).thenReturn(0L);
        when(mockSnowflake.nextIdStr()).thenReturn("transactionId");

        // Configure RemoteCallService.concurrentCallTSDPRenew(...).
        final RemoteModifyRespDTO remoteModifyRespDTO = new RemoteModifyRespDTO();
        remoteModifyRespDTO.setSuccess(true);
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("qweqwe12345678900");
        param.setUser("incontrolId");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("3020-01-01T15:59:59.000Z");
        tsdpSubscriptionDTO.setServiceName("serviceName");
        tsdpSubscriptionDTO.setServicePackage("servicePackage");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));
        when(mockRemoteCallService.concurrentCallTSDPRenew(param)).thenReturn(remoteModifyRespDTO);

        // Configure ApplicationContext.getBean(...).
        when(mockApplicationContext.getBean(RemoteSingleRenewalServiceImpl.class))
                .thenReturn(remoteSingleRenewalServiceImplUnderTest);

        // Run the test
        final CommonResult<String> result = remoteSingleRenewalServiceImplUnderTest.remoteSingleOperateRenewal(
                remoteSingleRenewalDTO);

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockRemoteRenewDetailRecordsMapper).insert(any(RemoteRenewDetailRecords.class));
        verify(mockRemoteRenewDetailRecordsMapper).updateById(any(RemoteRenewDetailRecords.class));
        verify(mockSubscriptionService).updateRemoteExpireDate(any(RemoteRenewDetailRecords.class));
        verify(mockManualModifyLogDOService).recordLog(any(RemoteRenewDetailRecords.class));
    }

    @Test
    public void testGetRemoteSinglePageList() {
        // Setup
        final RemoteSingleRenewalPageDTO pageDTO = new RemoteSingleRenewalPageDTO();
        pageDTO.setPageNo(0);
        pageDTO.setPageSize(0);
        pageDTO.setOperateTimeSort("asc");

        final PageResult<RemoteSinglePageListV0> expectedResult = new PageResult<>(
                List.of(RemoteSinglePageListV0.builder()
                        .operateTime("")
                        .carVin("carVin")
                        .operator("operator")
                        .renewalServiceName("远程车控")
                        .batchNo("1")
                        .build()), 0L);
        Page<RemoteRenewDetailRecords> pageResult = new Page<>();
        pageResult.setRecords(List.of(RemoteRenewDetailRecords.builder()
                .batchNo(1L)
                .carVin("carVin")
                .modifyStatus(1)
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .operator("operator")
                .build()));
        when(mockRemoteRenewDetailRecordsMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn(pageResult);

        // Run the test
        final PageResult<RemoteSinglePageListV0> result = remoteSingleRenewalServiceImplUnderTest.getRemoteSinglePageList(
                pageDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testUpdateRemoteRenewRecords() {
        // Setup
        final RemoteRenewDetailRecords renewRecords = RemoteRenewDetailRecords.builder()
                .id(0L)
                .batchNo(0L)
                .carVin("carVin")
                .modifyBeforeDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyAfterDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .modifyStatus(0)
                .errorDesc("errorDesc")
                .dataSource(0)
                .operator("operator")
                .build();
        final RemoteModifyRespDTO respDTO = new RemoteModifyRespDTO();
        respDTO.setSuccess(false);
        respDTO.setErrorMsg("errorDesc");

        // Run the test
        remoteSingleRenewalServiceImplUnderTest.updateRemoteRenewRecords(renewRecords, respDTO);

        // Verify the results
        verify(mockRemoteRenewDetailRecordsMapper).updateById(any(RemoteRenewDetailRecords.class));
    }

    @Test
    public void testVerifyParameters() {
        // Setup
        final RemoteSingleRenewalDTO remoteSingleRenewalDTO = new RemoteSingleRenewalDTO();
        remoteSingleRenewalDTO.setCarVin("qweqwe12345678900");
        remoteSingleRenewalDTO.setExpireDate("3020/01/01");

        final RemoteVerifyDTO verifyDTO = new RemoteVerifyDTO();
        verifyDTO.setCarVin("qweqwe12345678900");
        verifyDTO.setServiceName("远程车控");
        verifyDTO.setBeforeExpiryDate("2020/01/01");
        verifyDTO.setAfterExpiryDate("3020/01/01");
        final CommonResult<RemoteVerifyDTO> expectedResult = CommonResult.success(verifyDTO);

        // Configure SubscriptionService.getRemoteExpireDateByVin(...).
        final RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
        resultDTO.setCarVin("qweqwe12345678900");
        resultDTO.setExistInEcp(true);
        resultDTO.setPiviModel(true);
        resultDTO.setBeforeExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setAfterExpiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        resultDTO.setServiceDOList(List.of(SubscriptionServiceDO.builder()
                .incontrolId("incontrolId")
                .carVin("qweqwe12345678900")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .build()));
        when(mockSubscriptionService.getRemoteExpireDateByVin("qweqwe12345678900")).thenReturn(resultDTO);

        // Run the test
        final CommonResult<RemoteVerifyDTO> result = remoteSingleRenewalServiceImplUnderTest.verifyParameters(
                remoteSingleRenewalDTO);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testCheckProcessRecord_RemoteRenewDetailRecordsMapperReturnsNoItems() {
        // Setup
        when(mockRemoteRenewDetailRecordsMapper.getProcessRecordsByVin("carVin")).thenReturn(Collections.emptyList());

        // Run the test
        final boolean result = remoteSingleRenewalServiceImplUnderTest.checkProcessRecord("carVin");

        // Verify the results
        assertFalse(result);
    }
}
